{"adminDashboard": {"metadata": {"name": "WordPress Admin Dashboard", "version": "1.0.0", "framework": "NextJS", "description": "General admin dashboard design system for WordPress custom plugins"}, "layout": {"container": {"maxWidth": "1400px", "margin": "0 auto", "padding": "20px", "backgroundColor": "#f8fafc"}, "header": {"height": "80px", "backgroundColor": "#ffffff", "borderBottom": "1px solid #e2e8f0", "padding": "0 24px", "display": "flex", "alignItems": "center", "justifyContent": "space-between", "position": "sticky", "top": "0", "zIndex": "100"}, "content": {"backgroundColor": "#ffffff", "borderRadius": "12px", "boxShadow": "0 1px 3px rgba(0, 0, 0, 0.1)", "marginTop": "20px", "overflow": "hidden"}}, "designTokens": {"colors": {"primary": {"50": "#eff6ff", "100": "#dbeafe", "500": "#3b82f6", "600": "#2563eb", "700": "#1d4ed8", "900": "#1e3a8a"}, "secondary": {"50": "#f8fafc", "100": "#f1f5f9", "500": "#64748b", "600": "#475569", "700": "#334155"}, "success": {"50": "#f0fdf4", "500": "#22c55e", "600": "#16a34a"}, "warning": {"50": "#fffbeb", "500": "#f59e0b", "600": "#d97706"}, "error": {"50": "#fef2f2", "500": "#ef4444", "600": "#dc2626"}, "neutral": {"50": "#f9fafb", "100": "#f3f4f6", "200": "#e5e7eb", "300": "#d1d5db", "400": "#9ca3af", "500": "#6b7280", "600": "#4b5563", "700": "#374151", "800": "#1f2937", "900": "#111827"}}, "typography": {"fontFamily": {"primary": "'Inter', -apple-system, BlinkMacSystemFont, sans-serif", "mono": "'Fira Code', 'Consolas', monospace"}, "fontSize": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem", "4xl": "2.25rem"}, "fontWeight": {"normal": "400", "medium": "500", "semibold": "600", "bold": "700"}, "lineHeight": {"tight": "1.25", "normal": "1.5", "relaxed": "1.75"}}, "spacing": {"xs": "4px", "sm": "8px", "md": "12px", "lg": "16px", "xl": "20px", "2xl": "24px", "3xl": "32px", "4xl": "40px", "5xl": "48px"}, "borderRadius": {"sm": "4px", "md": "6px", "lg": "8px", "xl": "12px"}}, "tabSystem": {"container": {"borderBottom": "1px solid #e2e8f0"}, "tabList": {"display": "flex", "padding": "0 24px", "backgroundColor": "#f8fafc", "overflowX": "auto"}, "tab": {"default": {"padding": "16px 20px", "fontSize": "14px", "fontWeight": "500", "color": "#64748b", "backgroundColor": "transparent", "border": "none", "borderBottom": "2px solid transparent", "cursor": "pointer", "transition": "all 0.2s ease", "whiteSpace": "nowrap", "minWidth": "120px", "textAlign": "center"}, "active": {"color": "#2563eb", "borderBottomColor": "#2563eb", "backgroundColor": "#ffffff"}, "hover": {"color": "#334155", "backgroundColor": "#f1f5f9"}}, "tabPanel": {"padding": "24px", "backgroundColor": "#ffffff"}, "tabStructure": {"icon": {"size": "16px", "marginRight": "8px"}, "badge": {"backgroundColor": "#ef4444", "color": "#ffffff", "fontSize": "10px", "padding": "2px 6px", "borderRadius": "10px", "marginLeft": "8px"}}}, "components": {"card": {"default": {"backgroundColor": "#ffffff", "borderRadius": "8px", "boxShadow": "0 1px 3px rgba(0, 0, 0, 0.1)", "border": "1px solid #e2e8f0", "overflow": "hidden"}, "header": {"padding": "16px 20px", "borderBottom": "1px solid #e2e8f0", "backgroundColor": "#f8fafc"}, "body": {"padding": "20px"}, "footer": {"padding": "16px 20px", "borderTop": "1px solid #e2e8f0", "backgroundColor": "#f8fafc"}}, "button": {"primary": {"backgroundColor": "#2563eb", "color": "#ffffff", "padding": "10px 16px", "borderRadius": "6px", "border": "none", "fontSize": "14px", "fontWeight": "500", "cursor": "pointer", "transition": "all 0.2s ease"}, "secondary": {"backgroundColor": "#ffffff", "color": "#374151", "padding": "10px 16px", "borderRadius": "6px", "border": "1px solid #d1d5db", "fontSize": "14px", "fontWeight": "500", "cursor": "pointer", "transition": "all 0.2s ease"}, "success": {"backgroundColor": "#16a34a", "color": "#ffffff"}, "warning": {"backgroundColor": "#d97706", "color": "#ffffff"}, "error": {"backgroundColor": "#dc2626", "color": "#ffffff"}, "sizes": {"sm": {"padding": "6px 12px", "fontSize": "12px"}, "md": {"padding": "10px 16px", "fontSize": "14px"}, "lg": {"padding": "12px 20px", "fontSize": "16px"}}}, "input": {"default": {"width": "100%", "padding": "10px 12px", "borderRadius": "6px", "border": "1px solid #d1d5db", "fontSize": "14px", "backgroundColor": "#ffffff", "transition": "border-color 0.2s ease"}, "focus": {"borderColor": "#2563eb", "outline": "none", "boxShadow": "0 0 0 3px rgba(37, 99, 235, 0.1)"}, "error": {"borderColor": "#dc2626"}, "disabled": {"backgroundColor": "#f3f4f6", "color": "#9ca3af", "cursor": "not-allowed"}}, "select": {"default": {"width": "100%", "padding": "10px 12px", "borderRadius": "6px", "border": "1px solid #d1d5db", "fontSize": "14px", "backgroundColor": "#ffffff", "cursor": "pointer"}}, "textarea": {"default": {"width": "100%", "padding": "10px 12px", "borderRadius": "6px", "border": "1px solid #d1d5db", "fontSize": "14px", "backgroundColor": "#ffffff", "resize": "vertical", "minHeight": "100px"}}, "checkbox": {"size": "16px", "accentColor": "#2563eb"}, "radio": {"size": "16px", "accentColor": "#2563eb"}, "switch": {"width": "44px", "height": "24px", "borderRadius": "12px", "backgroundColor": "#d1d5db", "activeColor": "#2563eb"}}, "fieldGroups": {"formGroup": {"marginBottom": "20px"}, "label": {"display": "block", "fontSize": "14px", "fontWeight": "500", "color": "#374151", "marginBottom": "6px"}, "helperText": {"fontSize": "12px", "color": "#6b7280", "marginTop": "4px"}, "errorText": {"fontSize": "12px", "color": "#dc2626", "marginTop": "4px"}, "fieldRow": {"display": "grid", "gap": "16px", "gridTemplateColumns": {"1": "1fr", "2": "1fr 1fr", "3": "1fr 1fr 1fr", "4": "1fr 1fr 1fr 1fr"}}}, "notifications": {"toast": {"position": "fixed", "top": "20px", "right": "20px", "padding": "12px 16px", "borderRadius": "8px", "fontSize": "14px", "fontWeight": "500", "zIndex": "1000", "minWidth": "300px", "maxWidth": "500px"}, "alert": {"padding": "12px 16px", "borderRadius": "8px", "fontSize": "14px", "marginBottom": "16px", "border": "1px solid"}, "types": {"success": {"backgroundColor": "#f0fdf4", "borderColor": "#22c55e", "color": "#166534"}, "warning": {"backgroundColor": "#fffbeb", "borderColor": "#f59e0b", "color": "#92400e"}, "error": {"backgroundColor": "#fef2f2", "borderColor": "#ef4444", "color": "#991b1b"}, "info": {"backgroundColor": "#eff6ff", "borderColor": "#3b82f6", "color": "#1e40af"}}}, "tables": {"container": {"overflowX": "auto", "borderRadius": "8px", "border": "1px solid #e2e8f0"}, "table": {"width": "100%", "borderCollapse": "collapse"}, "header": {"backgroundColor": "#f8fafc", "borderBottom": "1px solid #e2e8f0"}, "headerCell": {"padding": "12px 16px", "fontSize": "12px", "fontWeight": "600", "color": "#374151", "textAlign": "left", "textTransform": "uppercase", "letterSpacing": "0.05em"}, "row": {"borderBottom": "1px solid #f3f4f6"}, "cell": {"padding": "12px 16px", "fontSize": "14px", "color": "#374151"}, "actions": {"display": "flex", "gap": "8px", "alignItems": "center"}}, "modals": {"overlay": {"position": "fixed", "top": "0", "left": "0", "width": "100vw", "height": "100vh", "backgroundColor": "rgba(0, 0, 0, 0.5)", "zIndex": "1000", "display": "flex", "alignItems": "center", "justifyContent": "center"}, "content": {"backgroundColor": "#ffffff", "borderRadius": "12px", "maxWidth": "90vw", "maxHeight": "90vh", "overflow": "auto", "boxShadow": "0 25px 50px -12px rgba(0, 0, 0, 0.25)"}, "header": {"padding": "20px 24px", "borderBottom": "1px solid #e2e8f0", "display": "flex", "alignItems": "center", "justifyContent": "space-between"}, "body": {"padding": "24px"}, "footer": {"padding": "16px 24px", "borderTop": "1px solid #e2e8f0", "display": "flex", "gap": "12px", "justifyContent": "flex-end"}}, "responsive": {"breakpoints": {"sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px", "2xl": "1536px"}, "adaptations": {"mobile": {"tabList": {"flexDirection": "column"}, "fieldRow": {"gridTemplateColumns": "1fr"}, "container": {"padding": "12px"}}, "tablet": {"fieldRow": {"gridTemplateColumns": "1fr 1fr"}}}}, "animations": {"transitions": {"fast": "0.15s ease", "normal": "0.2s ease", "slow": "0.3s ease"}, "fadeIn": {"keyframes": "fadeIn", "duration": "0.2s", "timingFunction": "ease-out"}, "slideUp": {"keyframes": "slideUp", "duration": "0.3s", "timingFunction": "ease-out"}}, "utilities": {"spacing": {"classes": ["m", "p", "mt", "mb", "ml", "mr", "pt", "pb", "pl", "pr"], "values": ["0", "1", "2", "3", "4", "5", "6", "8", "10", "12", "16", "20", "24"]}, "display": ["block", "inline-block", "flex", "inline-flex", "grid", "hidden"], "flexbox": {"direction": ["row", "column"], "justify": ["start", "center", "end", "between", "around"], "align": ["start", "center", "end", "stretch"]}, "text": {"align": ["left", "center", "right"], "size": ["xs", "sm", "base", "lg", "xl", "2xl"], "weight": ["normal", "medium", "semibold", "bold"]}}}}