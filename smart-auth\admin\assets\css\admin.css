/**
 * Smart Auth Admin Styles - Modern React-like Design
 *
 * @package SmartAuth
 * @since 1.0.0
 */

/* ==========================================================================
   CSS Variables for Consistent Design System
   ========================================================================== */

:root {
    /* Primary Colors - Updated to match design.json */
    --smart-auth-primary-50: #eff6ff;
    --smart-auth-primary-100: #dbeafe;
    --smart-auth-primary-500: #3b82f6;
    --smart-auth-primary-600: #2563eb;
    --smart-auth-primary-700: #1d4ed8;
    --smart-auth-primary-900: #1e3a8a;

    /* Secondary Colors */
    --smart-auth-secondary-50: #f8fafc;
    --smart-auth-secondary-100: #f1f5f9;
    --smart-auth-secondary-500: #64748b;
    --smart-auth-secondary-600: #475569;
    --smart-auth-secondary-700: #334155;

    /* Status Colors */
    --smart-auth-success-50: #f0fdf4;
    --smart-auth-success-500: #22c55e;
    --smart-auth-success-600: #16a34a;
    --smart-auth-warning-50: #fffbeb;
    --smart-auth-warning-500: #f59e0b;
    --smart-auth-warning-600: #d97706;
    --smart-auth-error-50: #fef2f2;
    --smart-auth-error-500: #ef4444;
    --smart-auth-error-600: #dc2626;

    /* Neutral Colors */
    --smart-auth-neutral-50: #f9fafb;
    --smart-auth-neutral-100: #f3f4f6;
    --smart-auth-neutral-200: #e5e7eb;
    --smart-auth-neutral-300: #d1d5db;
    --smart-auth-neutral-400: #9ca3af;
    --smart-auth-neutral-500: #6b7280;
    --smart-auth-neutral-600: #4b5563;
    --smart-auth-neutral-700: #374151;
    --smart-auth-neutral-800: #1f2937;
    --smart-auth-neutral-900: #111827;

    /* Typography */
    --smart-auth-font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --smart-auth-font-mono: 'Fira Code', 'Consolas', monospace;

    /* Font Sizes */
    --smart-auth-text-xs: 0.75rem;
    --smart-auth-text-sm: 0.875rem;
    --smart-auth-text-base: 1rem;
    --smart-auth-text-lg: 1.125rem;
    --smart-auth-text-xl: 1.25rem;
    --smart-auth-text-2xl: 1.5rem;
    --smart-auth-text-3xl: 1.875rem;
    --smart-auth-text-4xl: 2.25rem;

    /* Font Weights */
    --smart-auth-font-normal: 400;
    --smart-auth-font-medium: 500;
    --smart-auth-font-semibold: 600;
    --smart-auth-font-bold: 700;

    /* Line Heights */
    --smart-auth-leading-tight: 1.25;
    --smart-auth-leading-normal: 1.5;
    --smart-auth-leading-relaxed: 1.75;

    /* Spacing */
    --smart-auth-spacing-xs: 4px;
    --smart-auth-spacing-sm: 8px;
    --smart-auth-spacing-md: 12px;
    --smart-auth-spacing-lg: 16px;
    --smart-auth-spacing-xl: 20px;
    --smart-auth-spacing-2xl: 24px;
    --smart-auth-spacing-3xl: 32px;
    --smart-auth-spacing-4xl: 40px;
    --smart-auth-spacing-5xl: 48px;

    /* Border Radius */
    --smart-auth-radius-sm: 4px;
    --smart-auth-radius-md: 6px;
    --smart-auth-radius-lg: 8px;
    --smart-auth-radius-xl: 12px;

    /* Shadows */
    --smart-auth-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --smart-auth-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --smart-auth-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* ==========================================================================
   Main Wrapper and Layout
   ========================================================================== */

.smart-auth-admin-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background: var(--smart-auth-neutral-50);
    min-height: calc(100vh - 32px);
    font-family: var(--smart-auth-font-family);
}

/* ==========================================================================
   Header Section
   ========================================================================== */

.smart-auth-header {
    height: 80px;
    background: #ffffff;
    border-bottom: 1px solid var(--smart-auth-neutral-200);
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
    margin-bottom: 20px;
    border-radius: var(--smart-auth-radius-xl);
    box-shadow: var(--smart-auth-shadow-sm);
}

.smart-auth-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: var(--smart-auth-spacing-2xl);
}

.smart-auth-header-title {
    display: flex;
    align-items: center;
    gap: var(--smart-auth-spacing-lg);
}

.smart-auth-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--smart-auth-primary) 0%, var(--smart-auth-primary-dark) 100%);
    border-radius: var(--smart-auth-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--smart-auth-shadow-md);
}

.smart-auth-icon .dashicons {
    font-size: 32px;
    color: var(--smart-auth-white);
    width: 32px;
    height: 32px;
}

.smart-auth-title-text h1 {
    margin: 0;
    font-size: 32px;
    font-weight: 700;
    color: var(--smart-auth-gray-700);
    line-height: 1.2;
}

.smart-auth-subtitle {
    margin: var(--smart-auth-spacing-xs) 0 0 0;
    font-size: 16px;
    color: var(--smart-auth-gray-500);
    font-weight: 400;
}

/* ==========================================================================
   Status Overview
   ========================================================================== */

.smart-auth-status-summary {
    display: flex;
    align-items: center;
    gap: var(--smart-auth-spacing-lg);
}

.smart-auth-status-circle {
    position: relative;
    width: 80px;
    height: 80px;
}

.smart-auth-progress-ring {
    transform: rotate(-90deg);
}

.smart-auth-progress-ring-circle {
    transition: stroke-dashoffset 0.5s ease-in-out;
}

.smart-auth-status-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.smart-auth-status-percentage {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: var(--smart-auth-gray-700);
    line-height: 1;
}

.smart-auth-status-label {
    display: block;
    font-size: 12px;
    color: var(--smart-auth-gray-500);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 2px;
}

.smart-auth-status-details {
    display: flex;
    flex-direction: column;
    gap: var(--smart-auth-spacing-sm);
}

.smart-auth-status-item {
    display: flex;
    align-items: center;
    gap: var(--smart-auth-spacing-sm);
    font-size: 14px;
    color: var(--smart-auth-gray-600);
}

.smart-auth-status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.smart-auth-status-dot.configured {
    background: var(--smart-auth-success);
}

.smart-auth-status-dot.not-configured {
    background: var(--smart-auth-error);
}

.smart-auth-status-dot.warning {
    background: var(--smart-auth-warning);
}

/* ==========================================================================
   Main Content Area
   ========================================================================== */

.smart-auth-main-content {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    overflow: hidden;
}

.smart-auth-tab-panel {
    padding: 24px;
    background: #ffffff;
}

/* ==========================================================================
   Navigation Tabs
   ========================================================================== */

.smart-auth-nav-container {
    border-bottom: 1px solid var(--smart-auth-neutral-200);
}

.smart-auth-nav-tabs {
    display: flex;
    padding: 0 24px;
    background: var(--smart-auth-neutral-50);
    overflow-x: auto;
}

.smart-auth-nav-tab {
    padding: 16px 20px;
    font-size: var(--smart-auth-text-sm);
    font-weight: var(--smart-auth-font-medium);
    color: var(--smart-auth-secondary-500);
    background: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    text-align: center;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--smart-auth-spacing-sm);
}

.smart-auth-nav-tab:hover {
    color: var(--smart-auth-secondary-700);
    background: var(--smart-auth-neutral-100);
    text-decoration: none;
}

.smart-auth-nav-tab.active {
    color: var(--smart-auth-primary-600);
    border-bottom-color: var(--smart-auth-primary-600);
    background: #ffffff;
}

.smart-auth-nav-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.smart-auth-nav-text {
    font-size: 14px;
}

.smart-auth-nav-badge {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.smart-auth-nav-badge.configured {
    background: var(--smart-auth-success-light);
    color: var(--smart-auth-success);
}

.smart-auth-nav-badge.not-configured {
    background: var(--smart-auth-error-light);
    color: var(--smart-auth-error);
}

/* ==========================================================================
   Main Content and Cards
   ========================================================================== */

.smart-auth-main-content {
    margin-bottom: var(--smart-auth-spacing-xl);
}

.smart-auth-tab-panel {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.smart-auth-tab-header {
    margin-bottom: var(--smart-auth-spacing-xl);
}

.smart-auth-tab-header h2 {
    margin: 0 0 var(--smart-auth-spacing-sm) 0;
    font-size: 24px;
    font-weight: 700;
    color: var(--smart-auth-gray-700);
}

.smart-auth-tab-description {
    margin: 0;
    font-size: 16px;
    color: var(--smart-auth-gray-500);
    line-height: 1.5;
}

.smart-auth-cards-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--smart-auth-spacing-xl);
    align-items: start;
}

.smart-auth-card {
    background: #ffffff;
    border-radius: var(--smart-auth-radius-lg);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--smart-auth-neutral-200);
    overflow: hidden;
    transition: box-shadow 0.2s ease;
}

.smart-auth-card:hover {
    box-shadow: var(--smart-auth-shadow-md);
}

.smart-auth-card-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--smart-auth-neutral-200);
    background: var(--smart-auth-neutral-50);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.smart-auth-card-header h3 {
    margin: 0;
    font-size: var(--smart-auth-text-lg);
    font-weight: var(--smart-auth-font-semibold);
    color: var(--smart-auth-neutral-700);
}

.smart-auth-card-status {
    padding: var(--smart-auth-spacing-xs) var(--smart-auth-spacing-sm);
    border-radius: var(--smart-auth-radius-sm);
    font-size: var(--smart-auth-text-xs);
    font-weight: var(--smart-auth-font-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.smart-auth-card-status.configured {
    background: var(--smart-auth-success-50);
    color: var(--smart-auth-success-600);
}

.smart-auth-card-status.not-configured {
    background: var(--smart-auth-error-50);
    color: var(--smart-auth-error-600);
}

.smart-auth-card-body {
    padding: 20px;
}

.smart-auth-card-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--smart-auth-neutral-200);
    background: var(--smart-auth-neutral-50);
}

/* ==========================================================================
   Form Styles
   ========================================================================== */

.smart-auth-form {
    margin: 0;
}

.smart-auth-form-grid {
    display: grid;
    gap: var(--smart-auth-spacing-lg);
}

.smart-auth-form-group {
    display: flex;
    flex-direction: column;
    gap: var(--smart-auth-spacing-sm);
}

.smart-auth-label {
    display: flex;
    align-items: center;
    gap: var(--smart-auth-spacing-xs);
    font-size: 14px;
    font-weight: 600;
    color: var(--smart-auth-gray-700);
    margin: 0;
}

.smart-auth-required {
    color: var(--smart-auth-error);
    font-weight: 700;
}

.smart-auth-input,
.smart-auth-select,
.smart-auth-textarea {
    width: 100%;
    padding: var(--smart-auth-spacing-md);
    border: 2px solid var(--smart-auth-gray-300);
    border-radius: var(--smart-auth-radius-md);
    font-size: 14px;
    line-height: 1.4;
    color: var(--smart-auth-gray-700);
    background: var(--smart-auth-white);
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.smart-auth-input:focus,
.smart-auth-select:focus,
.smart-auth-textarea:focus {
    border-color: var(--smart-auth-primary);
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
    outline: none;
}

.smart-auth-input:disabled,
.smart-auth-select:disabled,
.smart-auth-textarea:disabled {
    background: var(--smart-auth-gray-100);
    color: var(--smart-auth-gray-400);
    cursor: not-allowed;
}

.smart-auth-input-error {
    border-color: var(--smart-auth-error) !important;
    box-shadow: 0 0 0 3px rgba(214, 54, 56, 0.1) !important;
}

.smart-auth-input-success {
    border-color: var(--smart-auth-success) !important;
    box-shadow: 0 0 0 3px rgba(0, 163, 42, 0.1) !important;
}

.smart-auth-field-error {
    margin-top: var(--smart-auth-spacing-xs);
    font-size: 12px;
    color: var(--smart-auth-error);
    font-weight: 500;
}

.smart-auth-help-text {
    margin: 0;
    font-size: 13px;
    color: var(--smart-auth-gray-500);
    line-height: 1.4;
}

.smart-auth-form-actions {
    display: flex;
    align-items: center;
    gap: var(--smart-auth-spacing-md);
    margin-top: var(--smart-auth-spacing-lg);
    padding-top: var(--smart-auth-spacing-lg);
    border-top: 1px solid var(--smart-auth-gray-200);
}

/* ==========================================================================
   Button Styles
   ========================================================================== */

.smart-auth-button {
    display: inline-flex;
    align-items: center;
    gap: var(--smart-auth-spacing-sm);
    padding: var(--smart-auth-spacing-md) var(--smart-auth-spacing-lg);
    border: 2px solid transparent;
    border-radius: var(--smart-auth-radius-md);
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    box-sizing: border-box;
}

.smart-auth-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.2);
}

.smart-auth-button-primary {
    background: var(--smart-auth-primary-600);
    color: #ffffff;
    border-color: var(--smart-auth-primary-600);
}

.smart-auth-button-primary:hover {
    background: var(--smart-auth-primary-700);
    border-color: var(--smart-auth-primary-700);
    color: #ffffff;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--smart-auth-shadow-md);
}

.smart-auth-button-secondary {
    background: #ffffff;
    color: var(--smart-auth-neutral-700);
    border-color: var(--smart-auth-neutral-300);
}

.smart-auth-button-secondary:hover {
    background: var(--smart-auth-neutral-50);
    border-color: var(--smart-auth-neutral-400);
    color: var(--smart-auth-neutral-700);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--smart-auth-shadow-md);
}

.smart-auth-button-success {
    background: var(--smart-auth-success-600);
    color: #ffffff;
    border-color: var(--smart-auth-success-600);
}

.smart-auth-button-warning {
    background: var(--smart-auth-warning-600);
    color: #ffffff;
    border-color: var(--smart-auth-warning-600);
}

.smart-auth-button-error {
    background: var(--smart-auth-error-600);
    color: #ffffff;
    border-color: var(--smart-auth-error-600);
}

.smart-auth-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.smart-auth-button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Override WordPress default button styles */
.smart-auth-button-primary.button-primary {
    background: var(--smart-auth-primary);
    border-color: var(--smart-auth-primary);
    color: var(--smart-auth-white);
    text-shadow: none;
    box-shadow: none;
    padding: var(--smart-auth-spacing-md) var(--smart-auth-spacing-lg);
    font-weight: 600;
    border-radius: var(--smart-auth-radius-md);
    border-width: 2px;
}

.smart-auth-button-primary.button-primary:hover {
    background: var(--smart-auth-primary-dark);
    border-color: var(--smart-auth-primary-dark);
    color: var(--smart-auth-white);
    transform: translateY(-1px);
    box-shadow: var(--smart-auth-shadow-md);
}

/* ==========================================================================
   Test Results and Status Indicators
   ========================================================================== */

.smart-auth-test-result {
    display: inline-flex;
    align-items: center;
    gap: var(--smart-auth-spacing-sm);
    padding: var(--smart-auth-spacing-sm) var(--smart-auth-spacing-md);
    border-radius: var(--smart-auth-radius-md);
    font-size: 13px;
    font-weight: 600;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-10px); }
    to { opacity: 1; transform: translateX(0); }
}

.smart-auth-test-result.success {
    background: var(--smart-auth-success-light);
    color: var(--smart-auth-success);
    border: 1px solid var(--smart-auth-success);
}

.smart-auth-test-result.error {
    background: var(--smart-auth-error-light);
    color: var(--smart-auth-error);
    border: 1px solid var(--smart-auth-error);
}

.smart-auth-test-result.loading {
    background: var(--smart-auth-gray-100);
    color: var(--smart-auth-gray-600);
    border: 1px solid var(--smart-auth-gray-300);
}

.smart-auth-test-result .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* ==========================================================================
   Help Cards and Documentation
   ========================================================================== */

.smart-auth-card-help {
    background: linear-gradient(135deg, var(--smart-auth-primary-light) 0%, var(--smart-auth-white) 100%);
}

.smart-auth-help-steps {
    display: flex;
    flex-direction: column;
    gap: var(--smart-auth-spacing-lg);
    margin-bottom: var(--smart-auth-spacing-xl);
}

.smart-auth-help-step {
    display: flex;
    gap: var(--smart-auth-spacing-md);
    align-items: flex-start;
}

.smart-auth-step-number {
    width: 32px;
    height: 32px;
    background: var(--smart-auth-primary);
    color: var(--smart-auth-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 700;
    flex-shrink: 0;
}

.smart-auth-step-content h4 {
    margin: 0 0 var(--smart-auth-spacing-xs) 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--smart-auth-gray-700);
}

.smart-auth-step-content p {
    margin: 0;
    font-size: 13px;
    color: var(--smart-auth-gray-600);
    line-height: 1.5;
}

.smart-auth-help-links {
    display: flex;
    flex-direction: column;
    gap: var(--smart-auth-spacing-sm);
    padding-top: var(--smart-auth-spacing-lg);
    border-top: 1px solid var(--smart-auth-gray-200);
}

.smart-auth-help-link {
    display: flex;
    align-items: center;
    gap: var(--smart-auth-spacing-sm);
    padding: var(--smart-auth-spacing-sm) var(--smart-auth-spacing-md);
    color: var(--smart-auth-primary);
    text-decoration: none;
    border-radius: var(--smart-auth-radius-sm);
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.smart-auth-help-link:hover {
    background: var(--smart-auth-primary-light);
    color: var(--smart-auth-primary-dark);
    text-decoration: none;
}

.smart-auth-help-link .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.smart-auth-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.smart-auth-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--smart-auth-gray-300);
    border-top: 2px solid var(--smart-auth-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Responsive Design - Updated to match design.json breakpoints
   ========================================================================== */

/* Large screens (1024px and up) - Desktop */
@media screen and (max-width: 1200px) {
    .smart-auth-cards-grid {
        grid-template-columns: 1fr;
        gap: var(--smart-auth-spacing-2xl);
    }

    .smart-auth-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--smart-auth-spacing-xl);
    }

    .smart-auth-status-summary {
        align-self: stretch;
        justify-content: center;
    }
}

@media screen and (max-width: 782px) {
    .smart-auth-admin-wrapper {
        padding: var(--smart-auth-spacing-md);
    }

    .smart-auth-header-content {
        padding: var(--smart-auth-spacing-lg);
    }

    .smart-auth-title-text h1 {
        font-size: 24px;
    }

    .smart-auth-nav-tabs {
        flex-direction: column;
        gap: var(--smart-auth-spacing-xs);
    }

    .smart-auth-nav-tab {
        justify-content: center;
        padding: var(--smart-auth-spacing-md);
    }

    .smart-auth-card-body,
    .smart-auth-card-footer {
        padding: var(--smart-auth-spacing-lg);
    }

    .smart-auth-form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .smart-auth-button {
        justify-content: center;
    }
}

@media screen and (max-width: 480px) {
    .smart-auth-header-title {
        flex-direction: column;
        text-align: center;
        gap: var(--smart-auth-spacing-sm);
    }

    .smart-auth-nav-icon {
        display: none;
    }

    .smart-auth-status-summary {
        flex-direction: column;
        gap: var(--smart-auth-spacing-md);
    }

    .smart-auth-help-step {
        flex-direction: column;
        gap: var(--smart-auth-spacing-sm);
    }

    .smart-auth-step-number {
        align-self: flex-start;
    }
}

/* ==========================================================================
   Accessibility Improvements
   ========================================================================== */

/* Focus indicators */
.smart-auth-nav-tab:focus,
.smart-auth-button:focus,
.smart-auth-input:focus,
.smart-auth-select:focus,
.smart-auth-textarea:focus {
    outline: 2px solid var(--smart-auth-primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --smart-auth-primary: #0066cc;
        --smart-auth-primary-dark: #004499;
        --smart-auth-gray-300: #666666;
        --smart-auth-gray-500: #333333;
        --smart-auth-gray-700: #000000;
    }

    .smart-auth-card {
        border: 2px solid var(--smart-auth-gray-300);
    }

    .smart-auth-button {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .smart-auth-progress-ring-progress {
        transition: none;
    }
}

/* Screen reader only content */
.smart-auth-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Skip link for keyboard navigation */
.smart-auth-skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--smart-auth-primary);
    color: var(--smart-auth-white);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--smart-auth-radius-sm);
    z-index: 1000;
}

.smart-auth-skip-link:focus {
    top: 6px;
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.smart-auth-text-center {
    text-align: center;
}

.smart-auth-text-left {
    text-align: left;
}

.smart-auth-text-right {
    text-align: right;
}

.smart-auth-hidden {
    display: none !important;
}

/* ARIA live regions for dynamic content */
.smart-auth-live-region {
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .smart-auth-nav-container,
    .smart-auth-button,
    .smart-auth-help-links {
        display: none;
    }

    .smart-auth-card {
        border: 1px solid var(--smart-auth-gray-300);
        box-shadow: none;
        break-inside: avoid;
    }

    .smart-auth-admin-wrapper {
        background: var(--smart-auth-white);
    }
}

/* ==========================================================================
   Additional Form Elements
   ========================================================================== */

/* Input with unit styling */
.smart-auth-input-with-unit {
    display: flex;
    align-items: center;
    gap: 8px;
}

.smart-auth-input-with-unit .smart-auth-input {
    flex: 1;
    min-width: 0;
}

.smart-auth-input-unit {
    color: var(--smart-auth-gray-600);
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

/* Input group styling */
.smart-auth-input-group {
    display: flex;
    align-items: stretch;
    gap: 0;
}

.smart-auth-input-group .smart-auth-input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
    flex: 1;
}

.smart-auth-input-group .smart-auth-button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 1px solid var(--smart-auth-gray-300);
    white-space: nowrap;
}

/* Full width form group */
.smart-auth-form-group-full {
    grid-column: 1 / -1;
}

/* Checkbox list styling */
.smart-auth-checkbox-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.smart-auth-checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.5;
}

.smart-auth-checkbox-label input[type="checkbox"] {
    margin: 0;
    margin-top: 2px;
    flex-shrink: 0;
}

.smart-auth-checkbox-text {
    flex: 1;
    color: var(--smart-auth-gray-700);
}

.smart-auth-checkbox-label:hover .smart-auth-checkbox-text {
    color: var(--smart-auth-gray-900);
}

/* Checkbox group styling */
.smart-auth-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.smart-auth-checkbox-group .smart-auth-checkbox-label {
    margin-bottom: 0;
}

/* Help features styling */
.smart-auth-help-features {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 16px;
}

.smart-auth-feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--smart-auth-gray-600);
}

.smart-auth-feature-item .dashicons {
    color: var(--smart-auth-success);
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Help steps styling improvements */
.smart-auth-help-steps {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.smart-auth-help-step {
    display: flex;
    gap: 16px;
    align-items: flex-start;
}

.smart-auth-step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--smart-auth-primary);
    color: white;
    border-radius: 50%;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
}

.smart-auth-step-content {
    flex: 1;
    min-width: 0;
}

.smart-auth-step-content h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--smart-auth-gray-900);
}

.smart-auth-step-content p {
    margin: 0;
    font-size: 14px;
    color: var(--smart-auth-gray-600);
    line-height: 1.5;
}

.smart-auth-step-content a {
    color: var(--smart-auth-primary);
    text-decoration: none;
}

.smart-auth-step-content a:hover {
    text-decoration: underline;
}

/* Responsive improvements for new elements */
@media (max-width: 768px) {
    .smart-auth-input-group {
        flex-direction: column;
    }

    .smart-auth-input-group .smart-auth-input {
        border-radius: var(--smart-auth-border-radius);
        border-right: 1px solid var(--smart-auth-gray-300);
    }

    .smart-auth-input-group .smart-auth-button {
        border-radius: var(--smart-auth-border-radius);
        border-left: 1px solid var(--smart-auth-gray-300);
        margin-top: 8px;
    }

    .smart-auth-input-with-unit {
        flex-direction: column;
        align-items: stretch;
        gap: 4px;
    }

    .smart-auth-input-unit {
        text-align: left;
        font-size: 12px;
    }

    .smart-auth-help-step {
        flex-direction: column;
        gap: 12px;
    }

    .smart-auth-step-number {
        align-self: flex-start;
    }
}
