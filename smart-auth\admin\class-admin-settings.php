<?php
/**
 * Smart Auth Admin Settings Class
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin Settings Class
 */
class Smart_Auth_Admin_Settings {
    
    /**
     * Page hook for admin page
     */
    private $page_hook;
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        add_action('wp_ajax_smart_auth_test_firebase', array($this, 'ajax_test_firebase'));
        add_action('wp_ajax_smart_auth_test_twilio', array($this, 'ajax_test_twilio'));
        add_action('wp_ajax_smart_auth_generate_jwt_secret', array($this, 'ajax_generate_jwt_secret'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        $this->page_hook = add_options_page(
            __('Smart Auth Settings', 'smart-auth'),
            __('Smart Auth', 'smart-auth'),
            'manage_options',
            'smart-auth-settings',
            array($this, 'render_settings_page')
        );
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on our settings page
        if ($hook !== $this->page_hook) {
            return;
        }
        
        // Enqueue admin CSS
        wp_enqueue_style(
            'smart-auth-admin',
            SMART_AUTH_PLUGIN_URL . 'admin/assets/css/admin.css',
            array(),
            SMART_AUTH_VERSION
        );
        
        // Enqueue admin JavaScript
        wp_enqueue_script(
            'smart-auth-admin',
            SMART_AUTH_PLUGIN_URL . 'admin/assets/js/admin.js',
            array('jquery'),
            SMART_AUTH_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('smart-auth-admin', 'smartAuthAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('smart_auth_admin_nonce'),
            'strings' => array(
                'testing' => __('Testing...', 'smart-auth'),
                'success' => __('Success!', 'smart-auth'),
                'error' => __('Error', 'smart-auth'),
                'connectionSuccess' => __('Connection successful!', 'smart-auth'),
                'connectionFailed' => __('Connection failed. Please check your settings.', 'smart-auth'),
                'secretGenerated' => __('New secret key generated!', 'smart-auth'),
                'copySuccess' => __('Copied to clipboard!', 'smart-auth'),
                'copyError' => __('Failed to copy to clipboard.', 'smart-auth'),
            )
        ));
    }

    /**
     * Render settings page
     */
    public function render_settings_page() {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'firebase';
        ?>
        <div class="wrap smart-auth-admin-wrapper">
            <!-- Header Section -->
            <div class="smart-auth-header">
                <div class="smart-auth-header-content">
                    <div class="smart-auth-header-title">
                        <div class="smart-auth-icon">
                            <span class="dashicons dashicons-shield-alt"></span>
                        </div>
                        <div class="smart-auth-title-text">
                            <h1><?php esc_html_e('Smart Auth', 'smart-auth'); ?></h1>
                            <p class="smart-auth-subtitle"><?php esc_html_e('Complete authentication solution for WordPress', 'smart-auth'); ?></p>
                        </div>
                    </div>
                    <div class="smart-auth-header-actions">
                        <div class="smart-auth-status-overview">
                            <?php $this->render_status_overview(); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <div class="smart-auth-nav-container">
                <nav class="smart-auth-nav-tabs" role="tablist" aria-label="<?php esc_attr_e('Smart Auth Settings Navigation', 'smart-auth'); ?>">
                    <a href="?page=smart-auth-settings&tab=firebase"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'firebase' ? 'active' : ''; ?>"
                       data-tab="firebase"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'firebase' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-firebase"
                       id="smart-auth-tab-firebase">
                        <span class="smart-auth-nav-icon dashicons dashicons-admin-network" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('Firebase', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('firebase'); ?></span>
                    </a>
                    <a href="?page=smart-auth-settings&tab=twilio"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'twilio' ? 'active' : ''; ?>"
                       data-tab="twilio"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'twilio' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-twilio"
                       id="smart-auth-tab-twilio">
                        <span class="smart-auth-nav-icon dashicons dashicons-smartphone" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('Twilio', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('twilio'); ?></span>
                    </a>
                    <a href="?page=smart-auth-settings&tab=jwt"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'jwt' ? 'active' : ''; ?>"
                       data-tab="jwt"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'jwt' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-jwt"
                       id="smart-auth-tab-jwt">
                        <span class="smart-auth-nav-icon dashicons dashicons-lock" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('JWT', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('jwt'); ?></span>
                    </a>
                    <a href="?page=smart-auth-settings&tab=user"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'user' ? 'active' : ''; ?>"
                       data-tab="user"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'user' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-user"
                       id="smart-auth-tab-user">
                        <span class="smart-auth-nav-icon dashicons dashicons-admin-users" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('Users', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('user'); ?></span>
                    </a>
                    <a href="?page=smart-auth-settings&tab=security"
                       class="smart-auth-nav-tab <?php echo $active_tab === 'security' ? 'active' : ''; ?>"
                       data-tab="security"
                       role="tab"
                       aria-selected="<?php echo $active_tab === 'security' ? 'true' : 'false'; ?>"
                       aria-controls="smart-auth-panel-security"
                       id="smart-auth-tab-security">
                        <span class="smart-auth-nav-icon dashicons dashicons-shield" aria-hidden="true"></span>
                        <span class="smart-auth-nav-text"><?php esc_html_e('Security', 'smart-auth'); ?></span>
                        <span class="smart-auth-nav-badge" aria-label="<?php esc_attr_e('Configuration status', 'smart-auth'); ?>"><?php echo $this->get_config_status('security'); ?></span>
                    </a>
                </nav>
            </div>

            <!-- Main Content Area -->
            <div class="smart-auth-main-content">
                <!-- Firebase Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="firebase"
                     id="smart-auth-panel-firebase"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-firebase"
                     style="<?php echo $active_tab === 'firebase' ? '' : 'display: none;'; ?>">
                    <?php $this->render_firebase_tab(); ?>
                </div>

                <!-- Twilio Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="twilio"
                     id="smart-auth-panel-twilio"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-twilio"
                     style="<?php echo $active_tab === 'twilio' ? '' : 'display: none;'; ?>">
                    <?php $this->render_twilio_tab(); ?>
                </div>

                <!-- JWT Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="jwt"
                     id="smart-auth-panel-jwt"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-jwt"
                     style="<?php echo $active_tab === 'jwt' ? '' : 'display: none;'; ?>">
                    <?php $this->render_jwt_tab(); ?>
                </div>

                <!-- User Settings Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="user"
                     id="smart-auth-panel-user"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-user"
                     style="<?php echo $active_tab === 'user' ? '' : 'display: none;'; ?>">
                    <?php $this->render_user_tab(); ?>
                </div>

                <!-- Security Tab -->
                <div class="smart-auth-tab-panel"
                     data-tab="security"
                     id="smart-auth-panel-security"
                     role="tabpanel"
                     aria-labelledby="smart-auth-tab-security"
                     style="<?php echo $active_tab === 'security' ? '' : 'display: none;'; ?>">
                    <?php $this->render_security_tab(); ?>
                </div>
            </div>

            <!-- Live region for announcements -->
            <div id="smart-auth-live-region" class="smart-auth-live-region" aria-live="polite" aria-atomic="true"></div>

            <!-- Hidden nonce field for AJAX requests -->
            <input type="hidden" id="smart_auth_admin_nonce" value="<?php echo wp_create_nonce('smart_auth_admin_nonce'); ?>" />
        </div>
        <?php
    }

    /**
     * Render status overview in header
     */
    private function render_status_overview() {
        $firebase_status = $this->get_config_status('firebase');
        $twilio_status = $this->get_config_status('twilio');
        $jwt_status = $this->get_config_status('jwt');

        $total_configured = 0;
        $total_services = 3;

        if ($firebase_status === 'configured') $total_configured++;
        if ($twilio_status === 'configured') $total_configured++;
        if ($jwt_status === 'configured') $total_configured++;

        $percentage = round(($total_configured / $total_services) * 100);
        ?>
        <div class="smart-auth-status-summary">
            <div class="smart-auth-status-circle">
                <svg class="smart-auth-progress-ring" width="60" height="60">
                    <circle class="smart-auth-progress-ring-circle" stroke="#e1e5e9" stroke-width="4" fill="transparent" r="26" cx="30" cy="30"/>
                    <circle class="smart-auth-progress-ring-progress" stroke="#007cba" stroke-width="4" fill="transparent" r="26" cx="30" cy="30"
                            style="stroke-dasharray: <?php echo 163.36; ?>; stroke-dashoffset: <?php echo 163.36 - (163.36 * $percentage / 100); ?>;"/>
                </svg>
                <div class="smart-auth-status-text">
                    <span class="smart-auth-status-percentage"><?php echo $percentage; ?>%</span>
                    <span class="smart-auth-status-label"><?php esc_html_e('Setup', 'smart-auth'); ?></span>
                </div>
            </div>
            <div class="smart-auth-status-details">
                <div class="smart-auth-status-item">
                    <span class="smart-auth-status-dot <?php echo $firebase_status; ?>"></span>
                    <?php esc_html_e('Firebase', 'smart-auth'); ?>
                </div>
                <div class="smart-auth-status-item">
                    <span class="smart-auth-status-dot <?php echo $twilio_status; ?>"></span>
                    <?php esc_html_e('Twilio', 'smart-auth'); ?>
                </div>
                <div class="smart-auth-status-item">
                    <span class="smart-auth-status-dot <?php echo $jwt_status; ?>"></span>
                    <?php esc_html_e('JWT', 'smart-auth'); ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Get configuration status for a service
     */
    private function get_config_status($service) {
        switch ($service) {
            case 'firebase':
                $settings = get_option('smart_auth_firebase_settings', array());
                $required_fields = array('project_id', 'api_key', 'auth_domain');
                break;
            case 'twilio':
                $settings = get_option('smart_auth_twilio_settings', array());
                $required_fields = array('account_sid', 'auth_token', 'verify_service_sid');
                break;
            case 'jwt':
                $settings = get_option('smart_auth_jwt_settings', array());
                $required_fields = array('secret_key');
                break;
            case 'user':
                return 'configured'; // User settings are always considered configured
            case 'security':
                return 'configured'; // Security settings are always considered configured
            default:
                return 'not-configured';
        }

        foreach ($required_fields as $field) {
            if (empty($settings[$field])) {
                return 'not-configured';
            }
        }

        return 'configured';
    }

    /**
     * Register settings for all tabs
     */
    public function register_settings() {
        // Firebase settings
        register_setting('smart_auth_firebase', 'smart_auth_firebase_settings', array(
            'sanitize_callback' => array($this, 'sanitize_firebase_settings')
        ));

        // Twilio settings
        register_setting('smart_auth_twilio', 'smart_auth_twilio_settings', array(
            'sanitize_callback' => array($this, 'sanitize_twilio_settings')
        ));

        // JWT settings
        register_setting('smart_auth_jwt', 'smart_auth_jwt_settings', array(
            'sanitize_callback' => array($this, 'sanitize_jwt_settings')
        ));

        // User settings
        register_setting('smart_auth_user', 'smart_auth_user_settings', array(
            'sanitize_callback' => array($this, 'sanitize_user_settings')
        ));

        // Security settings
        register_setting('smart_auth_security', 'smart_auth_security_settings', array(
            'sanitize_callback' => array($this, 'sanitize_security_settings')
        ));
    }

    /**
     * Render Firebase settings tab
     */
    private function render_firebase_tab() {
        $settings = get_option('smart_auth_firebase_settings', array());
        ?>
        <div class="smart-auth-tab-header">
            <h2><?php esc_html_e('Firebase Configuration', 'smart-auth'); ?></h2>
            <p class="smart-auth-tab-description">
                <?php esc_html_e('Configure Firebase Authentication for social login providers.', 'smart-auth'); ?>
            </p>
        </div>

        <div class="smart-auth-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('Firebase Settings', 'smart-auth'); ?></h3>
                <div class="smart-auth-card-status <?php echo $this->get_config_status('firebase'); ?>">
                    <?php echo $this->get_config_status('firebase') === 'configured' ? esc_html__('Configured', 'smart-auth') : esc_html__('Not Configured', 'smart-auth'); ?>
                </div>
            </div>
            <div class="smart-auth-card-body">
                <form method="post" action="options.php" class="smart-auth-form">
                    <?php
                    settings_fields('smart_auth_firebase');
                    do_settings_sections('smart_auth_firebase');
                    ?>

                    <div class="smart-auth-form-grid">
                        <div class="smart-auth-form-group">
                            <label for="firebase_project_id" class="smart-auth-label">
                                <?php esc_html_e('Project ID', 'smart-auth'); ?>
                                <span class="smart-auth-required">*</span>
                            </label>
                            <input type="text"
                                   id="firebase_project_id"
                                   name="smart_auth_firebase_settings[project_id]"
                                   value="<?php echo esc_attr(isset($settings['project_id']) ? $settings['project_id'] : ''); ?>"
                                   class="smart-auth-input"
                                   placeholder="your-project-id" />
                            <p class="smart-auth-help-text"><?php esc_html_e('Your Firebase project ID', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="firebase_api_key" class="smart-auth-label">
                                <?php esc_html_e('Web API Key', 'smart-auth'); ?>
                                <span class="smart-auth-required">*</span>
                            </label>
                            <input type="text"
                                   id="firebase_api_key"
                                   name="smart_auth_firebase_settings[api_key]"
                                   value="<?php echo esc_attr(isset($settings['api_key']) ? $settings['api_key'] : ''); ?>"
                                   class="smart-auth-input"
                                   placeholder="AIzaSyC..." />
                            <p class="smart-auth-help-text"><?php esc_html_e('Your Firebase Web API key', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="firebase_auth_domain" class="smart-auth-label">
                                <?php esc_html_e('Auth Domain', 'smart-auth'); ?>
                                <span class="smart-auth-required">*</span>
                            </label>
                            <input type="text"
                                   id="firebase_auth_domain"
                                   name="smart_auth_firebase_settings[auth_domain]"
                                   value="<?php echo esc_attr(isset($settings['auth_domain']) ? $settings['auth_domain'] : ''); ?>"
                                   class="smart-auth-input"
                                   placeholder="your-project-id.firebaseapp.com" />
                            <p class="smart-auth-help-text"><?php esc_html_e('Your Firebase authentication domain', 'smart-auth'); ?></p>
                        </div>
                    </div>

                    <div class="smart-auth-form-actions">
                        <button type="button" id="test-firebase-connection" class="smart-auth-button smart-auth-button-secondary">
                            <span class="dashicons dashicons-admin-network"></span>
                            <?php esc_html_e('Test Connection', 'smart-auth'); ?>
                        </button>
                        <div id="firebase-test-result" class="smart-auth-test-result"></div>
                    </div>

                    <div class="smart-auth-card-footer">
                        <?php submit_button(__('Save Firebase Settings', 'smart-auth'), 'primary smart-auth-button-primary', 'submit', false); ?>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }

    /**
     * Render Twilio settings tab
     */
    private function render_twilio_tab() {
        $settings = get_option('smart_auth_twilio_settings', array());
        ?>
        <div class="smart-auth-tab-header">
            <h2><?php esc_html_e('Twilio Configuration', 'smart-auth'); ?></h2>
            <p class="smart-auth-tab-description">
                <?php esc_html_e('Configure Twilio for SMS-based phone number authentication and verification.', 'smart-auth'); ?>
            </p>
        </div>

        <!-- Setup Guide Card -->
        <div class="smart-auth-card smart-auth-help-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('Setup Guide', 'smart-auth'); ?></h3>
            </div>
            <div class="smart-auth-card-body">
                <div class="smart-auth-help-steps">
                    <div class="smart-auth-help-step">
                        <span class="smart-auth-step-number">1</span>
                        <div class="smart-auth-step-content">
                            <h4><?php esc_html_e('Create Twilio Account', 'smart-auth'); ?></h4>
                            <p><?php esc_html_e('Sign up for a Twilio account at', 'smart-auth'); ?> <a href="https://www.twilio.com/try-twilio" target="_blank" rel="noopener">twilio.com</a></p>
                        </div>
                    </div>
                    <div class="smart-auth-help-step">
                        <span class="smart-auth-step-number">2</span>
                        <div class="smart-auth-step-content">
                            <h4><?php esc_html_e('Get API Credentials', 'smart-auth'); ?></h4>
                            <p><?php esc_html_e('Find your Account SID and Auth Token in the Twilio Console dashboard.', 'smart-auth'); ?></p>
                        </div>
                    </div>
                    <div class="smart-auth-help-step">
                        <span class="smart-auth-step-number">3</span>
                        <div class="smart-auth-step-content">
                            <h4><?php esc_html_e('Create Verify Service', 'smart-auth'); ?></h4>
                            <p><?php esc_html_e('Create a new Verify Service in the Twilio Console and copy the Service SID.', 'smart-auth'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Settings Card -->
        <div class="smart-auth-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('Twilio API Settings', 'smart-auth'); ?></h3>
                <div class="smart-auth-card-status <?php echo $this->get_config_status('twilio'); ?>">
                    <?php echo $this->get_config_status('twilio') === 'configured' ? esc_html__('Configured', 'smart-auth') : esc_html__('Not Configured', 'smart-auth'); ?>
                </div>
            </div>
            <div class="smart-auth-card-body">
                <form method="post" action="options.php" class="smart-auth-form">
                    <?php
                    settings_fields('smart_auth_twilio');
                    do_settings_sections('smart_auth_twilio');
                    ?>

                    <div class="smart-auth-form-grid">
                        <div class="smart-auth-form-group">
                            <label for="twilio_account_sid" class="smart-auth-label">
                                <?php esc_html_e('Account SID', 'smart-auth'); ?>
                                <span class="smart-auth-required">*</span>
                            </label>
                            <input type="text"
                                   id="twilio_account_sid"
                                   name="smart_auth_twilio_settings[account_sid]"
                                   value="<?php echo esc_attr(isset($settings['account_sid']) ? $settings['account_sid'] : ''); ?>"
                                   class="smart-auth-input"
                                   placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" />
                            <p class="smart-auth-help-text"><?php esc_html_e('Your Twilio Account SID from the Console Dashboard', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="twilio_auth_token" class="smart-auth-label">
                                <?php esc_html_e('Auth Token', 'smart-auth'); ?>
                                <span class="smart-auth-required">*</span>
                            </label>
                            <input type="password"
                                   id="twilio_auth_token"
                                   name="smart_auth_twilio_settings[auth_token]"
                                   value="<?php echo esc_attr(isset($settings['auth_token']) ? $settings['auth_token'] : ''); ?>"
                                   class="smart-auth-input"
                                   placeholder="••••••••••••••••••••••••••••••••" />
                            <p class="smart-auth-help-text"><?php esc_html_e('Your Twilio Auth Token (keep this secret)', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="twilio_verify_service_sid" class="smart-auth-label">
                                <?php esc_html_e('Verify Service SID', 'smart-auth'); ?>
                                <span class="smart-auth-required">*</span>
                            </label>
                            <input type="text"
                                   id="twilio_verify_service_sid"
                                   name="smart_auth_twilio_settings[verify_service_sid]"
                                   value="<?php echo esc_attr(isset($settings['verify_service_sid']) ? $settings['verify_service_sid'] : ''); ?>"
                                   class="smart-auth-input"
                                   placeholder="VAxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" />
                            <p class="smart-auth-help-text"><?php esc_html_e('Your Twilio Verify Service SID for OTP verification', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="twilio_default_region" class="smart-auth-label">
                                <?php esc_html_e('Default Region', 'smart-auth'); ?>
                            </label>
                            <select id="twilio_default_region"
                                    name="smart_auth_twilio_settings[default_region]"
                                    class="smart-auth-select">
                                <option value=""><?php esc_html_e('Auto (Recommended)', 'smart-auth'); ?></option>
                                <option value="us1" <?php selected(isset($settings['default_region']) ? $settings['default_region'] : '', 'us1'); ?>>
                                    <?php esc_html_e('US East (Virginia)', 'smart-auth'); ?>
                                </option>
                                <option value="ie1" <?php selected(isset($settings['default_region']) ? $settings['default_region'] : '', 'ie1'); ?>>
                                    <?php esc_html_e('Europe (Ireland)', 'smart-auth'); ?>
                                </option>
                                <option value="ap1" <?php selected(isset($settings['default_region']) ? $settings['default_region'] : '', 'ap1'); ?>>
                                    <?php esc_html_e('Asia Pacific (Singapore)', 'smart-auth'); ?>
                                </option>
                            </select>
                            <p class="smart-auth-help-text"><?php esc_html_e('Choose the Twilio region closest to your users for better performance', 'smart-auth'); ?></p>
                        </div>
                    </div>

                    <div class="smart-auth-form-actions">
                        <button type="button" id="test-twilio-connection" class="smart-auth-button smart-auth-button-secondary">
                            <span class="dashicons dashicons-smartphone"></span>
                            <?php esc_html_e('Test Connection', 'smart-auth'); ?>
                        </button>
                        <div id="twilio-test-result" class="smart-auth-test-result"></div>
                    </div>

                    <div class="smart-auth-card-footer">
                        <?php submit_button(__('Save Twilio Settings', 'smart-auth'), 'primary smart-auth-button-primary', 'submit', false); ?>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }

    /**
     * Render JWT settings tab
     */
    private function render_jwt_tab() {
        $settings = get_option('smart_auth_jwt_settings', array());
        ?>
        <div class="smart-auth-tab-header">
            <h2><?php esc_html_e('JWT Configuration', 'smart-auth'); ?></h2>
            <p class="smart-auth-tab-description">
                <?php esc_html_e('Configure JSON Web Token settings for secure authentication sessions.', 'smart-auth'); ?>
            </p>
        </div>

        <!-- Help Card -->
        <div class="smart-auth-card smart-auth-help-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('About JWT Authentication', 'smart-auth'); ?></h3>
            </div>
            <div class="smart-auth-card-body">
                <p><?php esc_html_e('JSON Web Tokens (JWT) provide a secure way to transmit information between parties. Smart Auth uses JWT to maintain user sessions after authentication.', 'smart-auth'); ?></p>
                <div class="smart-auth-help-features">
                    <div class="smart-auth-feature-item">
                        <span class="dashicons dashicons-shield-alt"></span>
                        <span><?php esc_html_e('Secure session management', 'smart-auth'); ?></span>
                    </div>
                    <div class="smart-auth-feature-item">
                        <span class="dashicons dashicons-clock"></span>
                        <span><?php esc_html_e('Configurable token expiration', 'smart-auth'); ?></span>
                    </div>
                    <div class="smart-auth-feature-item">
                        <span class="dashicons dashicons-update"></span>
                        <span><?php esc_html_e('Automatic token refresh', 'smart-auth'); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- JWT Settings Card -->
        <div class="smart-auth-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('JWT Settings', 'smart-auth'); ?></h3>
                <div class="smart-auth-card-status <?php echo $this->get_config_status('jwt'); ?>">
                    <?php echo $this->get_config_status('jwt') === 'configured' ? esc_html__('Configured', 'smart-auth') : esc_html__('Not Configured', 'smart-auth'); ?>
                </div>
            </div>
            <div class="smart-auth-card-body">
                <form method="post" action="options.php" class="smart-auth-form">
                    <?php
                    settings_fields('smart_auth_jwt');
                    do_settings_sections('smart_auth_jwt');
                    ?>

                    <div class="smart-auth-form-grid">
                        <div class="smart-auth-form-group smart-auth-form-group-full">
                            <label for="jwt_secret_key" class="smart-auth-label">
                                <?php esc_html_e('Secret Key', 'smart-auth'); ?>
                                <span class="smart-auth-required">*</span>
                            </label>
                            <div class="smart-auth-input-group">
                                <input type="password"
                                       id="jwt_secret_key"
                                       name="smart_auth_jwt_settings[secret_key]"
                                       value="<?php echo esc_attr(isset($settings['secret_key']) ? $settings['secret_key'] : ''); ?>"
                                       class="smart-auth-input"
                                       placeholder="••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••" />
                                <button type="button" id="generate-jwt-secret" class="smart-auth-button smart-auth-button-secondary">
                                    <span class="dashicons dashicons-admin-network"></span>
                                    <?php esc_html_e('Generate', 'smart-auth'); ?>
                                </button>
                            </div>
                            <p class="smart-auth-help-text"><?php esc_html_e('A secure random string used to sign JWT tokens. Keep this secret and never share it.', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="jwt_expiration" class="smart-auth-label">
                                <?php esc_html_e('Token Expiration', 'smart-auth'); ?>
                            </label>
                            <div class="smart-auth-input-with-unit">
                                <input type="number"
                                       id="jwt_expiration"
                                       name="smart_auth_jwt_settings[expiration]"
                                       value="<?php echo esc_attr(isset($settings['expiration']) ? $settings['expiration'] / HOUR_IN_SECONDS : 24); ?>"
                                       class="smart-auth-input"
                                       min="1"
                                       max="168" />
                                <span class="smart-auth-input-unit"><?php esc_html_e('hours', 'smart-auth'); ?></span>
                            </div>
                            <p class="smart-auth-help-text"><?php esc_html_e('How long JWT tokens remain valid (1-168 hours)', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="jwt_refresh_window" class="smart-auth-label">
                                <?php esc_html_e('Refresh Window', 'smart-auth'); ?>
                            </label>
                            <div class="smart-auth-input-with-unit">
                                <input type="number"
                                       id="jwt_refresh_window"
                                       name="smart_auth_jwt_settings[refresh_window]"
                                       value="<?php echo esc_attr(isset($settings['refresh_window']) ? $settings['refresh_window'] / DAY_IN_SECONDS : 7); ?>"
                                       class="smart-auth-input"
                                       min="1"
                                       max="30" />
                                <span class="smart-auth-input-unit"><?php esc_html_e('days', 'smart-auth'); ?></span>
                            </div>
                            <p class="smart-auth-help-text"><?php esc_html_e('How long users can refresh expired tokens (1-30 days)', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="jwt_algorithm" class="smart-auth-label">
                                <?php esc_html_e('Signing Algorithm', 'smart-auth'); ?>
                            </label>
                            <select id="jwt_algorithm"
                                    name="smart_auth_jwt_settings[algorithm]"
                                    class="smart-auth-select">
                                <option value="HS256" <?php selected(isset($settings['algorithm']) ? $settings['algorithm'] : 'HS256', 'HS256'); ?>>
                                    HS256 (<?php esc_html_e('Recommended', 'smart-auth'); ?>)
                                </option>
                                <option value="HS384" <?php selected(isset($settings['algorithm']) ? $settings['algorithm'] : 'HS256', 'HS384'); ?>>
                                    HS384
                                </option>
                                <option value="HS512" <?php selected(isset($settings['algorithm']) ? $settings['algorithm'] : 'HS256', 'HS512'); ?>>
                                    HS512
                                </option>
                            </select>
                            <p class="smart-auth-help-text"><?php esc_html_e('HMAC algorithm used to sign JWT tokens', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group smart-auth-form-group-full">
                            <div class="smart-auth-checkbox-group">
                                <label class="smart-auth-checkbox-label">
                                    <input type="checkbox"
                                           name="smart_auth_jwt_settings[enable_token_storage]"
                                           value="1"
                                           <?php checked(isset($settings['enable_token_storage']) ? $settings['enable_token_storage'] : true); ?> />
                                    <span class="smart-auth-checkbox-text">
                                        <?php esc_html_e('Store tokens in database', 'smart-auth'); ?>
                                    </span>
                                </label>
                                <p class="smart-auth-help-text"><?php esc_html_e('Store active tokens in database for better security and token management', 'smart-auth'); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="smart-auth-card-footer">
                        <?php submit_button(__('Save JWT Settings', 'smart-auth'), 'primary smart-auth-button-primary', 'submit', false); ?>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }

    /**
     * Render User settings tab
     */
    private function render_user_tab() {
        $settings = get_option('smart_auth_user_settings', array());
        ?>
        <div class="smart-auth-tab-header">
            <h2><?php esc_html_e('User Settings', 'smart-auth'); ?></h2>
            <p class="smart-auth-tab-description">
                <?php esc_html_e('Configure how Smart Auth handles user creation, data synchronization, and profile management.', 'smart-auth'); ?>
            </p>
        </div>

        <!-- User Creation Settings Card -->
        <div class="smart-auth-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('User Creation & Management', 'smart-auth'); ?></h3>
            </div>
            <div class="smart-auth-card-body">
                <form method="post" action="options.php" class="smart-auth-form">
                    <?php
                    settings_fields('smart_auth_user');
                    do_settings_sections('smart_auth_user');
                    ?>

                    <div class="smart-auth-form-grid">
                        <div class="smart-auth-form-group">
                            <div class="smart-auth-checkbox-group">
                                <label class="smart-auth-checkbox-label">
                                    <input type="checkbox"
                                           name="smart_auth_user_settings[auto_create_users]"
                                           value="1"
                                           <?php checked(isset($settings['auto_create_users']) ? $settings['auto_create_users'] : true); ?> />
                                    <span class="smart-auth-checkbox-text">
                                        <?php esc_html_e('Automatically create WordPress users', 'smart-auth'); ?>
                                    </span>
                                </label>
                                <p class="smart-auth-help-text"><?php esc_html_e('Create new WordPress users automatically when they authenticate for the first time', 'smart-auth'); ?></p>
                            </div>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="user_default_role" class="smart-auth-label">
                                <?php esc_html_e('Default User Role', 'smart-auth'); ?>
                            </label>
                            <select id="user_default_role"
                                    name="smart_auth_user_settings[default_role]"
                                    class="smart-auth-select">
                                <?php
                                $roles = get_editable_roles();
                                $selected_role = isset($settings['default_role']) ? $settings['default_role'] : 'subscriber';
                                foreach ($roles as $role_key => $role_info) {
                                    echo '<option value="' . esc_attr($role_key) . '"' . selected($selected_role, $role_key, false) . '>';
                                    echo esc_html(translate_user_role($role_info['name']));
                                    echo '</option>';
                                }
                                ?>
                            </select>
                            <p class="smart-auth-help-text"><?php esc_html_e('Default role assigned to new users created through Smart Auth', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="user_duplicate_strategy" class="smart-auth-label">
                                <?php esc_html_e('Duplicate User Strategy', 'smart-auth'); ?>
                            </label>
                            <select id="user_duplicate_strategy"
                                    name="smart_auth_user_settings[duplicate_strategy]"
                                    class="smart-auth-select">
                                <option value="login" <?php selected(isset($settings['duplicate_strategy']) ? $settings['duplicate_strategy'] : 'login', 'login'); ?>>
                                    <?php esc_html_e('Login existing user', 'smart-auth'); ?>
                                </option>
                                <option value="merge" <?php selected(isset($settings['duplicate_strategy']) ? $settings['duplicate_strategy'] : 'login', 'merge'); ?>>
                                    <?php esc_html_e('Merge with existing user', 'smart-auth'); ?>
                                </option>
                                <option value="error" <?php selected(isset($settings['duplicate_strategy']) ? $settings['duplicate_strategy'] : 'login', 'error'); ?>>
                                    <?php esc_html_e('Show error message', 'smart-auth'); ?>
                                </option>
                            </select>
                            <p class="smart-auth-help-text"><?php esc_html_e('What to do when a user tries to authenticate with an email that already exists', 'smart-auth'); ?></p>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Profile Synchronization Card -->
        <div class="smart-auth-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('Profile Data Synchronization', 'smart-auth'); ?></h3>
            </div>
            <div class="smart-auth-card-body">
                <form method="post" action="options.php" class="smart-auth-form">
                    <?php
                    settings_fields('smart_auth_user');
                    do_settings_sections('smart_auth_user');
                    ?>

                    <div class="smart-auth-form-grid">
                        <div class="smart-auth-form-group smart-auth-form-group-full">
                            <label class="smart-auth-label">
                                <?php esc_html_e('Sync Options', 'smart-auth'); ?>
                            </label>
                            <div class="smart-auth-checkbox-list">
                                <label class="smart-auth-checkbox-label">
                                    <input type="checkbox"
                                           name="smart_auth_user_settings[sync_display_name]"
                                           value="1"
                                           <?php checked(isset($settings['sync_display_name']) ? $settings['sync_display_name'] : true); ?> />
                                    <span class="smart-auth-checkbox-text">
                                        <?php esc_html_e('Sync display name from authentication provider', 'smart-auth'); ?>
                                    </span>
                                </label>
                                <label class="smart-auth-checkbox-label">
                                    <input type="checkbox"
                                           name="smart_auth_user_settings[sync_email]"
                                           value="1"
                                           <?php checked(isset($settings['sync_email']) ? $settings['sync_email'] : false); ?> />
                                    <span class="smart-auth-checkbox-text">
                                        <?php esc_html_e('Sync email from authentication provider (only if verified)', 'smart-auth'); ?>
                                    </span>
                                </label>
                                <label class="smart-auth-checkbox-label">
                                    <input type="checkbox"
                                           name="smart_auth_user_settings[sync_profile_picture]"
                                           value="1"
                                           <?php checked(isset($settings['sync_profile_picture']) ? $settings['sync_profile_picture'] : true); ?> />
                                    <span class="smart-auth-checkbox-text">
                                        <?php esc_html_e('Sync profile picture from authentication provider', 'smart-auth'); ?>
                                    </span>
                                </label>
                                <label class="smart-auth-checkbox-label">
                                    <input type="checkbox"
                                           name="smart_auth_user_settings[download_profile_picture]"
                                           value="1"
                                           <?php checked(isset($settings['download_profile_picture']) ? $settings['download_profile_picture'] : false); ?> />
                                    <span class="smart-auth-checkbox-text">
                                        <?php esc_html_e('Download and store profile pictures locally', 'smart-auth'); ?>
                                    </span>
                                </label>
                            </div>
                            <p class="smart-auth-help-text"><?php esc_html_e('Choose which user data to synchronize from authentication providers', 'smart-auth'); ?></p>
                        </div>
                    </div>

                    <div class="smart-auth-card-footer">
                        <?php submit_button(__('Save User Settings', 'smart-auth'), 'primary smart-auth-button-primary', 'submit', false); ?>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }

    /**
     * Render Security settings tab
     */
    private function render_security_tab() {
        $settings = get_option('smart_auth_security_settings', array());
        ?>
        <div class="smart-auth-tab-header">
            <h2><?php esc_html_e('Security Settings', 'smart-auth'); ?></h2>
            <p class="smart-auth-tab-description">
                <?php esc_html_e('Configure security options and access controls for Smart Auth.', 'smart-auth'); ?>
            </p>
        </div>

        <!-- Rate Limiting Card -->
        <div class="smart-auth-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('Rate Limiting & Protection', 'smart-auth'); ?></h3>
            </div>
            <div class="smart-auth-card-body">
                <form method="post" action="options.php" class="smart-auth-form">
                    <?php
                    settings_fields('smart_auth_security');
                    do_settings_sections('smart_auth_security');
                    ?>

                    <div class="smart-auth-form-grid">
                        <div class="smart-auth-form-group">
                            <div class="smart-auth-checkbox-group">
                                <label class="smart-auth-checkbox-label">
                                    <input type="checkbox"
                                           name="smart_auth_security_settings[enable_rate_limiting]"
                                           value="1"
                                           <?php checked(isset($settings['enable_rate_limiting']) ? $settings['enable_rate_limiting'] : true); ?> />
                                    <span class="smart-auth-checkbox-text">
                                        <?php esc_html_e('Enable rate limiting for authentication attempts', 'smart-auth'); ?>
                                    </span>
                                </label>
                                <p class="smart-auth-help-text"><?php esc_html_e('Prevents brute force attacks by limiting authentication attempts per IP address', 'smart-auth'); ?></p>
                            </div>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="security_max_attempts" class="smart-auth-label">
                                <?php esc_html_e('Max Attempts', 'smart-auth'); ?>
                            </label>
                            <div class="smart-auth-input-with-unit">
                                <input type="number"
                                       id="security_max_attempts"
                                       name="smart_auth_security_settings[rate_limit_attempts]"
                                       value="<?php echo esc_attr(isset($settings['rate_limit_attempts']) ? $settings['rate_limit_attempts'] : 5); ?>"
                                       class="smart-auth-input"
                                       min="1"
                                       max="20" />
                                <span class="smart-auth-input-unit"><?php esc_html_e('attempts', 'smart-auth'); ?></span>
                            </div>
                            <p class="smart-auth-help-text"><?php esc_html_e('Maximum authentication attempts per IP address within the time window', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="security_time_window" class="smart-auth-label">
                                <?php esc_html_e('Time Window', 'smart-auth'); ?>
                            </label>
                            <div class="smart-auth-input-with-unit">
                                <input type="number"
                                       id="security_time_window"
                                       name="smart_auth_security_settings[rate_limit_window]"
                                       value="<?php echo esc_attr(isset($settings['rate_limit_window']) ? $settings['rate_limit_window'] / 60 : 15); ?>"
                                       class="smart-auth-input"
                                       min="5"
                                       max="60" />
                                <span class="smart-auth-input-unit"><?php esc_html_e('minutes', 'smart-auth'); ?></span>
                            </div>
                            <p class="smart-auth-help-text"><?php esc_html_e('Time window for counting authentication attempts', 'smart-auth'); ?></p>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- OTP Security Card -->
        <div class="smart-auth-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('OTP Security Settings', 'smart-auth'); ?></h3>
            </div>
            <div class="smart-auth-card-body">
                <form method="post" action="options.php" class="smart-auth-form">
                    <?php
                    settings_fields('smart_auth_security');
                    do_settings_sections('smart_auth_security');
                    ?>

                    <div class="smart-auth-form-grid">
                        <div class="smart-auth-form-group">
                            <label for="security_otp_expiration" class="smart-auth-label">
                                <?php esc_html_e('OTP Expiration', 'smart-auth'); ?>
                            </label>
                            <div class="smart-auth-input-with-unit">
                                <input type="number"
                                       id="security_otp_expiration"
                                       name="smart_auth_security_settings[otp_expiration]"
                                       value="<?php echo esc_attr(isset($settings['otp_expiration']) ? $settings['otp_expiration'] / 60 : 10); ?>"
                                       class="smart-auth-input"
                                       min="5"
                                       max="30" />
                                <span class="smart-auth-input-unit"><?php esc_html_e('minutes', 'smart-auth'); ?></span>
                            </div>
                            <p class="smart-auth-help-text"><?php esc_html_e('How long OTP codes remain valid after being sent', 'smart-auth'); ?></p>
                        </div>

                        <div class="smart-auth-form-group">
                            <label for="security_resend_cooldown" class="smart-auth-label">
                                <?php esc_html_e('Resend Cooldown', 'smart-auth'); ?>
                            </label>
                            <div class="smart-auth-input-with-unit">
                                <input type="number"
                                       id="security_resend_cooldown"
                                       name="smart_auth_security_settings[otp_resend_cooldown]"
                                       value="<?php echo esc_attr(isset($settings['otp_resend_cooldown']) ? $settings['otp_resend_cooldown'] : 60); ?>"
                                       class="smart-auth-input"
                                       min="30"
                                       max="300" />
                                <span class="smart-auth-input-unit"><?php esc_html_e('seconds', 'smart-auth'); ?></span>
                            </div>
                            <p class="smart-auth-help-text"><?php esc_html_e('Minimum time between OTP resend requests', 'smart-auth'); ?></p>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Advanced Security Card -->
        <div class="smart-auth-card">
            <div class="smart-auth-card-header">
                <h3><?php esc_html_e('Advanced Security Options', 'smart-auth'); ?></h3>
            </div>
            <div class="smart-auth-card-body">
                <form method="post" action="options.php" class="smart-auth-form">
                    <?php
                    settings_fields('smart_auth_security');
                    do_settings_sections('smart_auth_security');
                    ?>

                    <div class="smart-auth-form-grid">
                        <div class="smart-auth-form-group smart-auth-form-group-full">
                            <div class="smart-auth-checkbox-list">
                                <label class="smart-auth-checkbox-label">
                                    <input type="checkbox"
                                           name="smart_auth_security_settings[secure_sessions]"
                                           value="1"
                                           <?php checked(isset($settings['secure_sessions']) ? $settings['secure_sessions'] : true); ?> />
                                    <span class="smart-auth-checkbox-text">
                                        <?php esc_html_e('Use secure session handling', 'smart-auth'); ?>
                                    </span>
                                </label>
                                <label class="smart-auth-checkbox-label">
                                    <input type="checkbox"
                                           name="smart_auth_security_settings[enable_https_only]"
                                           value="1"
                                           <?php checked(isset($settings['enable_https_only']) ? $settings['enable_https_only'] : true); ?> />
                                    <span class="smart-auth-checkbox-text">
                                        <?php esc_html_e('Require HTTPS for authentication', 'smart-auth'); ?>
                                    </span>
                                </label>
                                <label class="smart-auth-checkbox-label">
                                    <input type="checkbox"
                                           name="smart_auth_security_settings[enable_csrf_protection]"
                                           value="1"
                                           <?php checked(isset($settings['enable_csrf_protection']) ? $settings['enable_csrf_protection'] : true); ?> />
                                    <span class="smart-auth-checkbox-text">
                                        <?php esc_html_e('Enable CSRF protection', 'smart-auth'); ?>
                                    </span>
                                </label>
                                <label class="smart-auth-checkbox-label">
                                    <input type="checkbox"
                                           name="smart_auth_security_settings[enable_security_headers]"
                                           value="1"
                                           <?php checked(isset($settings['enable_security_headers']) ? $settings['enable_security_headers'] : true); ?> />
                                    <span class="smart-auth-checkbox-text">
                                        <?php esc_html_e('Enable security headers', 'smart-auth'); ?>
                                    </span>
                                </label>
                            </div>
                            <p class="smart-auth-help-text"><?php esc_html_e('Additional security measures to protect authentication processes', 'smart-auth'); ?></p>
                        </div>
                    </div>

                    <div class="smart-auth-card-footer">
                        <?php submit_button(__('Save Security Settings', 'smart-auth'), 'primary smart-auth-button-primary', 'submit', false); ?>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }

    /**
     * Sanitize Firebase settings
     */
    public function sanitize_firebase_settings($input) {
        $sanitized = array();

        if (isset($input['project_id'])) {
            $sanitized['project_id'] = sanitize_text_field($input['project_id']);
        }

        if (isset($input['api_key'])) {
            $sanitized['api_key'] = sanitize_text_field($input['api_key']);
        }

        if (isset($input['auth_domain'])) {
            $sanitized['auth_domain'] = sanitize_text_field($input['auth_domain']);
        }

        if (isset($input['storage_bucket'])) {
            $sanitized['storage_bucket'] = sanitize_text_field($input['storage_bucket']);
        }

        return $sanitized;
    }

    /**
     * Sanitize Twilio settings
     */
    public function sanitize_twilio_settings($input) {
        $sanitized = array();

        if (isset($input['account_sid'])) {
            $sanitized['account_sid'] = sanitize_text_field($input['account_sid']);
        }

        if (isset($input['auth_token'])) {
            $sanitized['auth_token'] = sanitize_text_field($input['auth_token']);
        }

        if (isset($input['verify_service_sid'])) {
            $sanitized['verify_service_sid'] = sanitize_text_field($input['verify_service_sid']);
        }

        if (isset($input['default_region'])) {
            $allowed_regions = array('', 'us1', 'ie1', 'ap1');
            $sanitized['default_region'] = in_array($input['default_region'], $allowed_regions) ? $input['default_region'] : '';
        }

        return $sanitized;
    }

    /**
     * Sanitize JWT settings
     */
    public function sanitize_jwt_settings($input) {
        $sanitized = array();

        if (isset($input['secret_key'])) {
            $sanitized['secret_key'] = sanitize_text_field($input['secret_key']);
        }

        if (isset($input['expiration'])) {
            $hours = absint($input['expiration']);
            $sanitized['expiration'] = max(1, min(168, $hours)) * HOUR_IN_SECONDS;
        }

        if (isset($input['refresh_window'])) {
            $days = absint($input['refresh_window']);
            $sanitized['refresh_window'] = max(1, min(30, $days)) * DAY_IN_SECONDS;
        }

        if (isset($input['algorithm'])) {
            $allowed_algorithms = array('HS256', 'HS384', 'HS512');
            $sanitized['algorithm'] = in_array($input['algorithm'], $allowed_algorithms) ? $input['algorithm'] : 'HS256';
        }

        if (isset($input['enable_token_storage'])) {
            $sanitized['enable_token_storage'] = (bool) $input['enable_token_storage'];
        }

        return $sanitized;
    }

    /**
     * Sanitize User settings
     */
    public function sanitize_user_settings($input) {
        $sanitized = array();

        if (isset($input['auto_create_users'])) {
            $sanitized['auto_create_users'] = (bool) $input['auto_create_users'];
        }

        if (isset($input['default_role'])) {
            $roles = get_editable_roles();
            $sanitized['default_role'] = array_key_exists($input['default_role'], $roles) ? $input['default_role'] : 'subscriber';
        }

        if (isset($input['duplicate_strategy'])) {
            $allowed_strategies = array('login', 'merge', 'error');
            $sanitized['duplicate_strategy'] = in_array($input['duplicate_strategy'], $allowed_strategies) ? $input['duplicate_strategy'] : 'login';
        }

        // Profile sync options
        $sync_options = array('sync_display_name', 'sync_email', 'sync_profile_picture', 'download_profile_picture');
        foreach ($sync_options as $option) {
            if (isset($input[$option])) {
                $sanitized[$option] = (bool) $input[$option];
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize Security settings
     */
    public function sanitize_security_settings($input) {
        $sanitized = array();

        if (isset($input['enable_rate_limiting'])) {
            $sanitized['enable_rate_limiting'] = (bool) $input['enable_rate_limiting'];
        }

        if (isset($input['rate_limit_attempts'])) {
            $sanitized['rate_limit_attempts'] = max(1, min(20, absint($input['rate_limit_attempts'])));
        }

        if (isset($input['rate_limit_window'])) {
            $minutes = max(5, min(60, absint($input['rate_limit_window'])));
            $sanitized['rate_limit_window'] = $minutes * 60;
        }

        if (isset($input['otp_expiration'])) {
            $minutes = max(5, min(30, absint($input['otp_expiration'])));
            $sanitized['otp_expiration'] = $minutes * 60;
        }

        if (isset($input['otp_resend_cooldown'])) {
            $sanitized['otp_resend_cooldown'] = max(30, min(300, absint($input['otp_resend_cooldown'])));
        }

        // Security options
        $security_options = array('secure_sessions', 'enable_https_only', 'enable_csrf_protection', 'enable_security_headers');
        foreach ($security_options as $option) {
            if (isset($input[$option])) {
                $sanitized[$option] = (bool) $input[$option];
            }
        }

        return $sanitized;
    }

    /**
     * AJAX handler for testing Firebase connection
     */
    public function ajax_test_firebase() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_admin_nonce')) {
            wp_die(__('Security check failed.', 'smart-auth'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'smart-auth'));
        }

        // Get form data
        $project_id = sanitize_text_field($_POST['project_id']);
        $api_key = sanitize_text_field($_POST['api_key']);
        $auth_domain = sanitize_text_field($_POST['auth_domain']);

        // Basic validation
        if (empty($project_id) || empty($api_key) || empty($auth_domain)) {
            wp_send_json_error(array(
                'message' => __('Please fill in all required fields.', 'smart-auth')
            ));
        }

        // Test Firebase configuration by making a simple API call
        $test_url = "https://identitytoolkit.googleapis.com/v1/projects/{$project_id}:lookup?key={$api_key}";

        $response = wp_remote_post($test_url, array(
            'timeout' => 10,
            'headers' => array(
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode(array(
                'localId' => array('test')
            ))
        ));

        if (is_wp_error($response)) {
            wp_send_json_error(array(
                'message' => __('Connection failed: ', 'smart-auth') . $response->get_error_message()
            ));
        }

        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code === 200 || $response_code === 400) {
            // 400 is expected for invalid localId, but means API is accessible
            wp_send_json_success(array(
                'message' => __('Firebase connection successful!', 'smart-auth')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Firebase API returned error code: ', 'smart-auth') . $response_code
            ));
        }
    }

    /**
     * AJAX handler for testing Twilio connection
     */
    public function ajax_test_twilio() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_admin_nonce')) {
            wp_die(__('Security check failed.', 'smart-auth'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'smart-auth'));
        }

        // Get form data
        $account_sid = sanitize_text_field($_POST['account_sid']);
        $auth_token = sanitize_text_field($_POST['auth_token']);
        $verify_service_sid = sanitize_text_field($_POST['verify_service_sid']);

        // Basic validation
        if (empty($account_sid) || empty($auth_token) || empty($verify_service_sid)) {
            wp_send_json_error(array(
                'message' => __('Please fill in all required fields.', 'smart-auth')
            ));
        }

        // Test Twilio API by checking the Verify Service
        $url = "https://verify.twilio.com/v2/Services/{$verify_service_sid}";

        $response = wp_remote_get($url, array(
            'timeout' => 10,
            'headers' => array(
                'Authorization' => 'Basic ' . base64_encode($account_sid . ':' . $auth_token)
            )
        ));

        if (is_wp_error($response)) {
            wp_send_json_error(array(
                'message' => __('Connection failed: ', 'smart-auth') . $response->get_error_message()
            ));
        }

        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code === 200) {
            wp_send_json_success(array(
                'message' => __('Twilio connection successful!', 'smart-auth')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Twilio API returned error code: ', 'smart-auth') . $response_code
            ));
        }
    }

    /**
     * AJAX handler for generating JWT secret
     */
    public function ajax_generate_jwt_secret() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_admin_nonce')) {
            wp_die(__('Security check failed.', 'smart-auth'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'smart-auth'));
        }

        // Generate new secret
        $secret = wp_generate_password(64, true, true);

        wp_send_json_success(array(
            'secret' => $secret,
            'message' => __('New JWT secret generated successfully!', 'smart-auth'),
        ));
    }
}
