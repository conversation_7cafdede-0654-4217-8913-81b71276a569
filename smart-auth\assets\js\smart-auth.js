/**
 * Smart Auth Frontend JavaScript
 *
 * @package SmartAuth
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * Smart Auth main object
     */
    window.SmartAuth = {
        
        /**
         * Initialize Smart Auth
         */
        init: function() {
            this.bindEvents();
            this.initFirebase();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            $(document).on('click', '.smart-auth-google-button', this.handleGoogleAuth);
            $(document).on('click', '.smart-auth-facebook-button', this.handleFacebookAuth);
            $(document).on('click', '.smart-auth-apple-button', this.handleAppleAuth);
            $(document).on('click', '.smart-auth-phone-button', this.handlePhoneAuth);
            $(document).on('submit', '.smart-auth-otp-form', this.handleOTPSubmit);
            $(document).on('click', '.smart-auth-resend-otp', this.handleResendOTP);
        },
        
        /**
         * Initialize Firebase
         */
        initFirebase: function() {
            // Check if smartAuth global is available
            if (typeof smartAuth === 'undefined') {
                console.error('Smart Auth: Configuration not loaded');
                return false;
            }

            // Check if Firebase SDK is loaded
            if (typeof firebase === 'undefined') {
                console.warn('Smart Auth: Firebase SDK not loaded');
                return false;
            }

            // Check if Firebase is configured
            if (!smartAuth.firebaseConfig || !smartAuth.firebaseConfig.apiKey) {
                console.warn('Smart Auth: Firebase not configured');
                return false;
            }

            try {
                // Initialize Firebase if not already initialized
                if (!firebase.apps.length) {
                    firebase.initializeApp(smartAuth.firebaseConfig);
                }

                this.auth = firebase.auth();
                console.log('Smart Auth: Firebase initialized successfully');
                return true;
            } catch (error) {
                console.error('Smart Auth: Firebase initialization failed', error);
                return false;
            }
        },
        
        /**
         * Handle Google authentication
         */
        handleGoogleAuth: function(e) {
            e.preventDefault();

            var $button = $(this);
            SmartAuth.showLoading($button);

            // Check if Firebase is properly initialized
            if (!SmartAuth.auth) {
                console.error('Smart Auth: Firebase not initialized for Google auth');
                SmartAuth.showError(smartAuth.strings.configError || smartAuth.strings.error);
                SmartAuth.hideLoading($button);
                return;
            }

            try {
                var provider = new firebase.auth.GoogleAuthProvider();
                provider.addScope('email');
                provider.addScope('profile');

                SmartAuth.auth.signInWithPopup(provider)
                    .then(function(result) {
                        console.log('Smart Auth: Google auth successful', result.user.uid);
                        return result.user.getIdToken();
                    })
                    .then(function(idToken) {
                        SmartAuth.verifyFirebaseToken(idToken, 'google', $button);
                    })
                    .catch(function(error) {
                        console.error('Smart Auth: Google auth error', error);
                        var errorMessage = SmartAuth.getFirebaseErrorMessage(error);
                        SmartAuth.showError(errorMessage);
                        SmartAuth.hideLoading($button);
                        SmartAuth.showButtonError($button);
                    });
            } catch (error) {
                console.error('Smart Auth: Google auth setup error', error);
                SmartAuth.showError(smartAuth.strings.configError || smartAuth.strings.error);
                SmartAuth.hideLoading($button);
            }
        },
        
        /**
         * Handle Facebook authentication
         */
        handleFacebookAuth: function(e) {
            e.preventDefault();

            var $button = $(this);
            SmartAuth.showLoading($button);

            if (!SmartAuth.auth) {
                console.error('Smart Auth: Firebase not initialized for Facebook auth');
                SmartAuth.showError(smartAuth.strings.configError || smartAuth.strings.error);
                SmartAuth.hideLoading($button);
                return;
            }

            try {
                var provider = new firebase.auth.FacebookAuthProvider();
                provider.addScope('email');

                SmartAuth.auth.signInWithPopup(provider)
                    .then(function(result) {
                        console.log('Smart Auth: Facebook auth successful', result.user.uid);
                        return result.user.getIdToken();
                    })
                    .then(function(idToken) {
                        SmartAuth.verifyFirebaseToken(idToken, 'facebook', $button);
                    })
                    .catch(function(error) {
                        console.error('Smart Auth: Facebook auth error', error);
                        var errorMessage = SmartAuth.getFirebaseErrorMessage(error);
                        SmartAuth.showError(errorMessage);
                        SmartAuth.hideLoading($button);
                    });
            } catch (error) {
                console.error('Smart Auth: Facebook auth setup error', error);
                SmartAuth.showError(smartAuth.strings.configError || smartAuth.strings.error);
                SmartAuth.hideLoading($button);
            }
        },
        
        /**
         * Handle Apple authentication
         */
        handleAppleAuth: function(e) {
            e.preventDefault();

            var $button = $(this);
            SmartAuth.showLoading($button);

            if (!SmartAuth.auth) {
                console.error('Smart Auth: Firebase not initialized for Apple auth');
                SmartAuth.showError(smartAuth.strings.configError || smartAuth.strings.error);
                SmartAuth.hideLoading($button);
                return;
            }

            try {
                var provider = new firebase.auth.OAuthProvider('apple.com');
                provider.addScope('email');
                provider.addScope('name');

                SmartAuth.auth.signInWithPopup(provider)
                    .then(function(result) {
                        console.log('Smart Auth: Apple auth successful', result.user.uid);
                        return result.user.getIdToken();
                    })
                    .then(function(idToken) {
                        SmartAuth.verifyFirebaseToken(idToken, 'apple', $button);
                    })
                    .catch(function(error) {
                        console.error('Smart Auth: Apple auth error', error);
                        var errorMessage = SmartAuth.getFirebaseErrorMessage(error);
                        SmartAuth.showError(errorMessage);
                        SmartAuth.hideLoading($button);
                    });
            } catch (error) {
                console.error('Smart Auth: Apple auth setup error', error);
                SmartAuth.showError(smartAuth.strings.configError || smartAuth.strings.error);
                SmartAuth.hideLoading($button);
            }
        },
        
        /**
         * Handle phone authentication
         */
        handlePhoneAuth: function(e) {
            e.preventDefault();
            
            var $form = $(this).closest('.smart-auth-form');
            var phoneNumber = $form.find('.smart-auth-phone-input').val();
            
            if (!phoneNumber) {
                SmartAuth.showError(smartAuth.strings.invalidPhone);
                return;
            }
            
            var $button = $(this);
            SmartAuth.showLoading($button);
            
            SmartAuth.sendPhoneOTP(phoneNumber, $button);
        },
        
        /**
         * Send phone OTP
         */
        sendPhoneOTP: function(phoneNumber, $button) {
            $.ajax({
                url: smartAuth.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'smart_auth_send_otp',
                    phone_number: phoneNumber,
                    nonce: smartAuth.nonce
                },
                success: function(response) {
                    if (response.success) {
                        SmartAuth.showOTPForm(phoneNumber, response.data.session_info);
                        SmartAuth.showSuccess(smartAuth.strings.otpSent);
                    } else {
                        SmartAuth.showError(response.data.message || smartAuth.strings.error);
                    }
                },
                error: function() {
                    SmartAuth.showError(smartAuth.strings.error);
                },
                complete: function() {
                    SmartAuth.hideLoading($button);
                }
            });
        },
        
        /**
         * Handle OTP form submit
         */
        handleOTPSubmit: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var phoneNumber = $form.find('.smart-auth-phone-number').val();
            var otpCode = $form.find('.smart-auth-otp-code').val();
            var sessionInfo = $form.find('.smart-auth-session-info').val();
            
            if (!otpCode) {
                SmartAuth.showError(smartAuth.strings.otpInvalid);
                return;
            }
            
            var $button = $form.find('.smart-auth-verify-otp');
            SmartAuth.showLoading($button);
            
            SmartAuth.verifyPhoneOTP(phoneNumber, otpCode, sessionInfo, $button);
        },
        
        /**
         * Verify phone OTP
         */
        verifyPhoneOTP: function(phoneNumber, otpCode, sessionInfo, $button) {
            $.ajax({
                url: smartAuth.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'smart_auth_verify_otp',
                    phone_number: phoneNumber,
                    otp_code: otpCode,
                    session_info: sessionInfo,
                    nonce: smartAuth.nonce
                },
                success: function(response) {
                    if (response.success) {
                        SmartAuth.handleAuthSuccess(response.data);
                    } else {
                        SmartAuth.showError(response.data.message || smartAuth.strings.otpInvalid);
                    }
                },
                error: function() {
                    SmartAuth.showError(smartAuth.strings.error);
                },
                complete: function() {
                    SmartAuth.hideLoading($button);
                }
            });
        },
        
        /**
         * Verify Firebase token
         */
        verifyFirebaseToken: function(idToken, provider, $button) {
            $.ajax({
                url: smartAuth.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'smart_auth_firebase_verify',
                    id_token: idToken,
                    provider: provider,
                    nonce: smartAuth.nonce
                },
                success: function(response) {
                    if (response.success) {
                        console.log('Smart Auth: Token verification successful');
                        SmartAuth.handleAuthSuccess(response.data, $button);
                    } else {
                        console.error('Smart Auth: Token verification failed', response.data);
                        SmartAuth.showError(response.data.message || smartAuth.strings.authenticationFailed);
                        if ($button) SmartAuth.hideLoading($button);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Smart Auth: AJAX error during token verification', error);
                    SmartAuth.showError(smartAuth.strings.networkError || smartAuth.strings.error);
                    if ($button) SmartAuth.hideLoading($button);
                }
            });
        },
        
        /**
         * Handle authentication success
         */
        handleAuthSuccess: function(data, $button) {
            SmartAuth.showSuccess(data.message || smartAuth.strings.success);

            // Get redirect URL from button data or response data
            var redirectUrl = '';
            if ($button && $button.data('redirect-url')) {
                redirectUrl = $button.data('redirect-url');
            } else if (data.redirect_url) {
                redirectUrl = data.redirect_url;
            }

            // Redirect or reload
            if (redirectUrl) {
                setTimeout(function() {
                    window.location.href = redirectUrl;
                }, 1500);
            } else {
                setTimeout(function() {
                    window.location.reload();
                }, 1500);
            }
        },
        
        /**
         * Show OTP form
         */
        showOTPForm: function(phoneNumber, sessionInfo) {
            var $form = $('.smart-auth-form');
            var otpFormHtml = '<div class="smart-auth-otp-section">' +
                '<form class="smart-auth-otp-form">' +
                '<input type="hidden" class="smart-auth-phone-number" value="' + phoneNumber + '">' +
                '<input type="hidden" class="smart-auth-session-info" value="' + sessionInfo + '">' +
                '<label for="otp-code">' + smartAuth.strings.otpCode + '</label>' +
                '<input type="text" class="smart-auth-otp-code" id="otp-code" maxlength="6" required>' +
                '<button type="submit" class="smart-auth-verify-otp">' + smartAuth.strings.verify + '</button>' +
                '<button type="button" class="smart-auth-resend-otp">' + smartAuth.strings.resend + '</button>' +
                '</form>' +
                '</div>';
            
            $form.find('.smart-auth-providers').hide();
            $form.append(otpFormHtml);
        },

        /**
         * Get user-friendly error message from Firebase error
         */
        getFirebaseErrorMessage: function(error) {
            var errorCode = error.code || '';
            var errorMessage = error.message || '';

            switch (errorCode) {
                case 'auth/popup-closed-by-user':
                    return 'Authentication was cancelled. Please try again.';
                case 'auth/popup-blocked':
                    return 'Popup was blocked by your browser. Please allow popups and try again.';
                case 'auth/network-request-failed':
                    return smartAuth.strings.networkError;
                case 'auth/too-many-requests':
                    return 'Too many failed attempts. Please try again later.';
                case 'auth/user-disabled':
                    return 'This account has been disabled. Please contact support.';
                case 'auth/invalid-phone-number':
                    return smartAuth.strings.invalidPhone;
                case 'auth/invalid-verification-code':
                    return smartAuth.strings.otpInvalid;
                default:
                    return errorMessage || smartAuth.strings.firebaseError || smartAuth.strings.error;
            }
        },

        /**
         * Show loading state
         */
        showLoading: function($button) {
            $button.prop('disabled', true);
            $button.addClass('loading');
            $button.removeClass('error');
            $button.data('original-text', $button.text());
        },

        /**
         * Hide loading state
         */
        hideLoading: function($button) {
            $button.prop('disabled', false);
            $button.removeClass('loading');
            // Restore text after a brief delay for better UX
            setTimeout(function() {
                if ($button.data('original-text')) {
                    $button.text($button.data('original-text'));
                }
            }, 100);
        },

        /**
         * Show error state on button
         */
        showButtonError: function($button) {
            $button.addClass('error');
            setTimeout(function() {
                $button.removeClass('error');
            }, 2000);
        },
        
        /**
         * Show error message
         */
        showError: function(message) {
            SmartAuth.showMessage(message, 'error');
        },
        
        /**
         * Show success message
         */
        showSuccess: function(message) {
            SmartAuth.showMessage(message, 'success');
        },
        
        /**
         * Show message
         */
        showMessage: function(message, type) {
            var $message = $('<div class="smart-auth-message smart-auth-' + type + '">' + message + '</div>');
            $('.smart-auth-form').prepend($message);
            
            setTimeout(function() {
                $message.fadeOut(function() {
                    $message.remove();
                });
            }, 5000);
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        SmartAuth.init();
    });
    
})(jQuery);
