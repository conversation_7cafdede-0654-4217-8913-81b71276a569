<?php
/**
 * Authentication Form Template
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Default attributes
$defaults = array(
    'title' => __('Sign In', 'smart-auth'),
    'description' => '',
    'show_providers' => array('google', 'facebook', 'phone'),
    'form_style' => 'default',
    'redirect_url' => '',
    'show_labels' => true,
    'css_class' => '',
);

$atts = wp_parse_args($atts, $defaults);

// Sanitize attributes
$title = sanitize_text_field($atts['title']);
$description = sanitize_textarea_field($atts['description']);
$show_providers = is_array($atts['show_providers']) ? $atts['show_providers'] : explode(',', $atts['show_providers']);
$form_style = sanitize_text_field($atts['form_style']);
$redirect_url = esc_url($atts['redirect_url']);
$show_labels = filter_var($atts['show_labels'], FILTER_VALIDATE_BOOLEAN);
$css_class = sanitize_html_class($atts['css_class']);

// Build CSS classes
$form_classes = array(
    'smart-auth-form',
    'smart-auth-form-' . $form_style,
);

if (!empty($css_class)) {
    $form_classes[] = $css_class;
}

$form_class = implode(' ', $form_classes);

// Get Firebase settings to check what's enabled
$firebase_settings = get_option('smart_auth_firebase_settings', array());
$user_settings = get_option('smart_auth_user_settings', array());

// Check if user is already logged in
if (is_user_logged_in()) {
    $current_user = wp_get_current_user();
    ?>
    <div class="<?php echo esc_attr($form_class); ?>">
        <div class="smart-auth-logged-in">
            <h3><?php esc_html_e('Welcome back!', 'smart-auth'); ?></h3>
            <p><?php printf(esc_html__('You are logged in as %s', 'smart-auth'), '<strong>' . esc_html($current_user->display_name) . '</strong>'); ?></p>
            <a href="<?php echo esc_url(wp_logout_url()); ?>" class="smart-auth-button smart-auth-logout-button">
                <?php esc_html_e('Logout', 'smart-auth'); ?>
            </a>
        </div>
    </div>
    <?php
    return;
}
?>

<div class="<?php echo esc_attr($form_class); ?>" data-redirect-url="<?php echo esc_attr($redirect_url); ?>">
    <?php if (!empty($title)) : ?>
        <h3 class="smart-auth-form-title"><?php echo esc_html($title); ?></h3>
    <?php endif; ?>
    
    <?php if (!empty($description)) : ?>
        <p class="smart-auth-form-description"><?php echo esc_html($description); ?></p>
    <?php endif; ?>
    
    <div class="smart-auth-providers">
        <?php if (in_array('google', $show_providers) && !empty($firebase_settings['api_key'])) : ?>
            <button type="button" class="smart-auth-button smart-auth-google-button" data-provider="google">
                <svg width="18" height="18" viewBox="0 0 18 18" style="margin-right: 8px;">
                    <path fill="#4285F4" d="M16.51 8H8.98v3h4.3c-.18 1-.74 1.48-1.6 2.04v2.01h2.6a7.8 7.8 0 0 0 2.38-5.88c0-.57-.05-.66-.15-1.18z"/>
                    <path fill="#34A853" d="M8.98 17c2.16 0 3.97-.72 5.3-1.94l-2.6-2.04a4.8 4.8 0 0 1-7.18-2.53H1.83v2.07A8 8 0 0 0 8.98 17z"/>
                    <path fill="#FBBC05" d="M4.5 10.49a4.8 4.8 0 0 1 0-3.07V5.35H1.83a8 8 0 0 0 0 7.28l2.67-2.14z"/>
                    <path fill="#EA4335" d="M8.98 4.72c1.16 0 2.23.4 3.06 1.2l2.3-2.3A8 8 0 0 0 1.83 5.35L4.5 7.42a4.77 4.77 0 0 1 4.48-2.7z"/>
                </svg>
                <?php if ($show_labels) : ?>
                    <?php esc_html_e('Continue with Google', 'smart-auth'); ?>
                <?php else : ?>
                    <?php esc_html_e('Google', 'smart-auth'); ?>
                <?php endif; ?>
            </button>
        <?php endif; ?>
        
        <?php if (in_array('facebook', $show_providers) && !empty($firebase_settings['api_key'])) : ?>
            <button type="button" class="smart-auth-button smart-auth-facebook-button" data-provider="facebook">
                <svg width="18" height="18" viewBox="0 0 24 24" style="margin-right: 8px;" fill="currentColor">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                <?php if ($show_labels) : ?>
                    <?php esc_html_e('Continue with Facebook', 'smart-auth'); ?>
                <?php else : ?>
                    <?php esc_html_e('Facebook', 'smart-auth'); ?>
                <?php endif; ?>
            </button>
        <?php endif; ?>
        
        <?php if (in_array('apple', $show_providers) && !empty($firebase_settings['api_key'])) : ?>
            <button type="button" class="smart-auth-button smart-auth-apple-button" data-provider="apple">
                <svg width="18" height="18" viewBox="0 0 24 24" style="margin-right: 8px;" fill="currentColor">
                    <path d="M12.017 0C8.396 0 8.025.044 8.025.044c0 .467.02 1.05.02 1.05C8.045 3.65 9.792 5.474 12.017 5.474s3.972-1.824 3.972-4.38c0 0-.03-.583-.03-1.05C15.959.044 15.588 0 12.017 0zm7.624 9.409c-.877-1.426-2.08-2.141-3.467-2.141-1.235 0-2.185.728-3.467.728-1.282 0-2.232-.728-3.467-.728-1.387 0-2.59.715-3.467 2.141C4.895 10.835 4.5 12.5 4.5 14.5c0 4.5 2.5 9.5 5.5 9.5 1.5 0 2.5-1 3.5-1s2 1 3.5 1c3 0 5.5-5 5.5-9.5 0-2-0.395-3.665-1.359-5.091z"/>
                </svg>
                <?php if ($show_labels) : ?>
                    <?php esc_html_e('Continue with Apple', 'smart-auth'); ?>
                <?php else : ?>
                    <?php esc_html_e('Apple', 'smart-auth'); ?>
                <?php endif; ?>
            </button>
        <?php endif; ?>
        
        <?php if (in_array('phone', $show_providers)) : ?>
            <div class="smart-auth-phone-section">
                <input type="tel" class="smart-auth-phone-input" placeholder="<?php esc_attr_e('Enter your phone number', 'smart-auth'); ?>" />
                <button type="button" class="smart-auth-button smart-auth-phone-button" data-provider="phone">
                    <svg width="18" height="18" viewBox="0 0 24 24" style="margin-right: 8px;" fill="currentColor">
                        <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                    </svg>
                    <?php if ($show_labels) : ?>
                        <?php esc_html_e('Continue with Phone', 'smart-auth'); ?>
                    <?php else : ?>
                        <?php esc_html_e('Phone', 'smart-auth'); ?>
                    <?php endif; ?>
                </button>
            </div>
        <?php endif; ?>
    </div>
    
    <?php if (empty($show_providers) || (empty($firebase_settings['api_key']) && !in_array('phone', $show_providers))) : ?>
        <div class="smart-auth-message smart-auth-info">
            <p><?php esc_html_e('Authentication providers are not configured. Please contact the site administrator.', 'smart-auth'); ?></p>
        </div>
    <?php endif; ?>
</div>

<?php
// Firebase SDK is now loaded by the shortcode handler
// This ensures proper loading order and prevents conflicts
?>
